var t = require("../../../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.normalizeFulfillmentOfferingsResponse = exports.normalizeCheckoutPreviewsResponse = exports.default = void 0;
var e = require("../../../lib/dot-prop"),
  o = require("../../../utils/data-format");

function r(t, r, i) {
  var s = (0, o.formatPriceForDisplay)((0, e.get)(i, "fulfillment.total", 0), t, r),
    a = (0, e.get)(i, "items.details.price", 0),
    n = (0, o.formatPriceForDisplay)(a, t, r),
    l = (0, o.formatPriceForDisplay)(i.total, t, r),
    u = "",
    c = "",
    f = (0, e.get)(i, "fulfillment.details.discount", 0) + (0, e.get)(i, "items.details.discount", 0),
    p = (0, e.get)(i, "fulfillment.details.price", 0) + (0, e.get)(i, "items.details.price", 0);
  return f > 0 && (u = (0, o.formatPriceForDisplay)(f, t, r), c = (0, o.formatPriceForDisplay)(p, t, r)), {
    shippingTotal: s,
    subtotal: n,
    total: l,
    totalWithoutDiscount: c,
    discountTotal: u
  }
}
var i = function(e) {
  var i = e.result.data.response,
    s = i.currency,
    a = i.locale,
    n = i.totals,
    l = i.fulfillmentGroups,
    u = i.promotionCodes,
    c = r(s, a, n),
    f = [],
    p = [],
    m = l.find((function(t) {
      return "SHIP" === t.fulfillmentDetails.type
    }));
  return m.items.forEach((function(e) {
    var r = "",
      i = !1;
    e.itemCosts.promotionDiscounts && e.itemCosts.promotionDiscounts.forEach((function(t) {
      if (t.amount) {
        var n = {
          amount: t.amount * e.quantity,
          formattedAmount: (0, o.formatPriceForDisplay)(t.amount * e.quantity, s, a),
          displayName: t.displayName,
          code: t.code
        };
        r = (0, o.formatPromoCodeDisplayName)(t.displayName), f.push(n), i = !0
      }
    })), p.push(t(t({
      id: e.id,
      skuId: e.skuId,
      quantity: e.quantity
    }, e.itemCosts.priceInfo), {}, {
      promotionDiscountName: r,
      hasPromotionDiscount: i
    }))
  })), {
    total: n.total,
    promotionCodes: u,
    promotionDiscounts: f,
    formattedTotals: c,
    totals: n,
    contactInfo: m.contactInfo,
    recipient: m.recipient,
    checkoutItems: p
  }
};
exports.normalizeCheckoutPreviewsResponse = i;
exports.normalizeFulfillmentOfferingsResponse = function(e) {
  var r = function(t) {
      for (var e = Object.keys(t.header), o = 0; o < e.length; o++)
        if ("x-b3-traceid" === e[o].toLowerCase()) return t.header[e[o]];
      return ""
    }(e.result),
    i = e.result.data.response,
    s = i.country,
    a = i.currency,
    n = i.locale,
    l = i.items,
    u = i.fulfillmentGroups,
    c = i.locations,
    f = u.find((function(t) {
      return "SHIP" === t.type
    }));
  return f.priceOffers && (f.priceOffers = f.priceOffers.map((function(e) {
    var r, i = null == e || null === (r = e.price) || void 0 === r ? void 0 : r.total,
      s = (0, o.formatPriceForDisplay)(i, a, n);
    return t(t({}, e), {}, {
      displayShippingPrice: s
    })
  }))), {
    country: s,
    currency: a,
    locale: n,
    items: l,
    fulfillmentGroup: f,
    locations: c,
    b3TraceId: r
  }
};
exports.default = {
  normalizeCheckoutPreviewsResponse: i
};