var e = require("../../../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.normalizePaymentPreviewRequestBody = exports.normalizeCheckoutPreviewsRequestBody = exports.getHeader = exports.default = void 0;
var t = require("../../../utils/shipping"),
  r = function(e, t) {
    var r = {
      Authorization: "Bearer ".concat(e)
    };
    return "string" == typeof t && "" !== t && (r["X-B3-TraceId"] = t), r
  };
exports.getHeader = r;
exports.normalizePaymentPreviewRequestBody = function(t) {
  var r = t.checkoutId,
    i = t.items,
    o = t.shippingAddress;
  return {
    checkoutId: r,
    items: i.map((function(t) {
      return e(e({}, t), {}, {
        shippingAddress: o
      })
    }))
  }
};
var i = function(e) {
  var r = e.phoneNumber,
    i = e.items,
    o = e.promoCodes,
    n = e.shippingOptionId,
    s = e.shippingAddress,
    a = e.invoice,
    u = e.recipient,
    d = e.contactInfo,
    p = {
      phoneNumber: r,
      country: "CN",
      currency: "CNY",
      locale: "zh_CN",
      channel: "WECHAT",
      items: i.map((function(e) {
        var r = e.id,
          i = e.skuId,
          o = e.quantity,
          a = e.offer,
          p = e.fulfillmentOfferings.find((function(e) {
            return e.priceOfferId === n
          }));
        return {
          id: r,
          skuId: i,
          offer: a,
          quantity: o,
          fulfillmentDetails: {
            type: "SHIP",
            getBy: p.getBy,
            location: {
              postalAddress: (0, t.shippingAddressNormalizer)(s),
              type: "address/shipping"
            },
            validationToken: p.validationToken
          },
          recipient: u,
          contactInfo: d
        }
      }))
    };
  return o && o.length > 0 && (p.promotionCodes = o), a && (p.invoiceInfo = a), {
    request: p
  }
};
exports.normalizeCheckoutPreviewsRequestBody = i;
exports.default = {
  getHeader: r,
  normalizeCheckoutPreviewsRequestBody: i
};