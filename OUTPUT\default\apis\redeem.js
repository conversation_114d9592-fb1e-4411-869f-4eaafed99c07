var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.completeDailySign = function(e) {
  return y.apply(this, arguments)
}, exports.completeShareTask = function(e) {
  return g.apply(this, arguments)
}, exports.completeStrollTask = function(e) {
  return x.apply(this, arguments)
}, exports.exchangePrize = function(e) {
  return i.apply(this, arguments)
}, exports.getExchangeDetail = function(e) {
  return s.apply(this, arguments)
}, exports.getExchangeListV2 = function() {
  return u.apply(this, arguments)
}, exports.getExchangeOrderDetail = function(e) {
  return l.apply(this, arguments)
}, exports.getMyPrizeList = function() {
  return o.apply(this, arguments)
}, exports.getPointsLog = function() {
  return a.apply(this, arguments)
}, exports.getRedeemCenterInfo = function() {
  return c.apply(this, arguments)
}, exports.getRedeemEntranceInfo = function() {
  return p.apply(this, arguments)
}, exports.getRedeemPrizeAnnouncement = function() {
  return h.apply(this, arguments)
}, exports.getRedeemTaskShareId = function(e) {
  return v.apply(this, arguments)
}, exports.getRuleConfigByKey = function(e) {
  return m.apply(this, arguments)
}, exports.getSharingConfigByKey = function(e) {
  return b.apply(this, arguments)
}, exports.getUserPointsAtTime = function() {
  return d.apply(this, arguments)
}, exports.submitRedeemInfo = function(e) {
  return f.apply(this, arguments)
};
var r = require("./common"),
  n = "/onemp";

function c() {
  return (c = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/redeem_center_info/v2")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function u() {
  return (u = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/tab_exchange_list/v2")).then((function(e) {
            return e.data
          })));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function a() {
  return (a = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/points_log/v1")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function p() {
  return (p = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/redeem_entrance_info/v2")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function s() {
  return (s = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/exchange_detail/v1/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function o() {
  return (o = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/redeemed_rewards/v2")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function i() {
  return (i = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/exchange_prize/v1/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function f() {
  return (f = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.post("".concat(n, "/redeem/submit_prize_info/v1"), c));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function l() {
  return (l = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/exchange_order_detail/v1/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function h() {
  return (h = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/redeem_prize_show/v2")).then((function(e) {
            return e.data
          })));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function m() {
  return (m = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.wechatApiInst.get("".concat(n, "/redeem/config_rule/v1/").concat(c)).then((function(e) {
            return e.data
          })));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function d() {
  return (d = t(e().mark((function t() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/user_points_at_time/v2")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function v() {
  return (v = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.t0 = r.redeemApiInst, e.t1 = "".concat(n, "/redeem/share_friend/v2/").concat(c), e.t2 = "Bearer ", e.next = 6, (0, r.getGCAuthToken)();
        case 6:
          return e.t3 = e.sent, e.t4 = e.t2.concat.call(e.t2, e.t3), e.t5 = {
            "mp-gc-token": e.t4
          }, e.t6 = {
            header: e.t5
          }, e.abrupt("return", e.t0.get.call(e.t0, e.t1, e.t6).then((function(e) {
            return e.data
          })));
        case 13:
          return e.prev = 13, e.t7 = e.catch(0), e.abrupt("return", Promise.reject(e.t7));
        case 16:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 13]
    ])
  })))).apply(this, arguments)
}

function g() {
  return (g = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.wechatApiInst.post("".concat(n, "/redeem/complete_share_task/v2"), c));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function x() {
  return (x = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/complete_stroll_task/v2/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function y() {
  return (y = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/complete_daily_sign/v2/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function b() {
  return (b = t(e().mark((function t(c) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", r.redeemApiInst.get("".concat(n, "/redeem/sharing_config/v1/").concat(c)));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}