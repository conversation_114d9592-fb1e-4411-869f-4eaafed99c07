Object.defineProperty(exports, "__esModule", {
  value: !0
}), Object.defineProperty(exports, "XMLHttpRequest", {
  enumerable: !0,
  get: function() {
    return e.default
  }
}), Object.defineProperty(exports, "localStorage", {
  enumerable: !0,
  get: function() {
    return t.default
  }
});
var e = r(require("./XMLHttpRequest")),
  t = r(require("./localStorage"));

function r(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}