<float-view pointX="{{left}}" pointY="{{top}}" vHeight="{{vHeight}}" vWidth="{{vWidth}}" wx:if="{{isShow}}">
    <view class="appBackBtn-wrapper g-flex-center" style="width:{{selfWidth}}px;">
        <view class="icon icon-left-arrow"></view>
        <button appParameter="NikeOnWeChat" bind:tap="onTap" binderror="onError" class="back-button" hoverClass="press-down-button" openType="launchApp">{{appName}}</button>
        <view bind:tap="onClose" catch:touchmove="" class="icon icon-close" hoverClass="press-down-button"></view>
    </view>
</float-view>
