var e = require("../@babel/runtime/helpers/objectSpread2"),
  t = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.cancelStoreServiceInteraction = function(e) {
  return u.apply(this, arguments)
}, exports.fetchStoreServiceInteraction = function(e) {
  return s.apply(this, arguments)
}, exports.fetchStoreServicesById = function(e) {
  return o.apply(this, arguments)
}, exports.fetchStoreZonesById = function(e) {
  return c.apply(this, arguments)
}, exports.requestStoreServiceInteraction = function(e) {
  return a.apply(this, arguments)
};
var n = require("./common"),
  i = "/store";

function o() {
  return (o = r(t().mark((function e(r) {
    var o, c, a = arguments;
    return t().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = a.length > 1 && void 0 !== a[1] ? a[1] : {}, c = o.timeoutRetry, e.next = 3, (0, n.nikeApiInst)({
            timeoutRetry: c
          }).get("".concat(i, "/store_services/v1/?filter=storeId(").concat(r, ")"));
        case 3:
          return e.abrupt("return", e.sent.data.objects);
        case 4:
        case "end":
          return e.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function c() {
  return (c = r(t().mark((function e(r) {
    var o, c, a, s = arguments;
    return t().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = s.length > 1 && void 0 !== s[1] ? s[1] : {}, c = o.timeoutRetry, a = getApp().globalData.appConfig.nikeApiCallerId, e.next = 4, (0, n.nikeApiInst)({
            timeoutRetry: c
          }).get("".concat(i, "/zones/v1/?filter=storeId(").concat(r, ")"), {
            header: {
              "nike-api-caller-id": a,
              appid: wx.getAccountInfoSync().miniProgram.appId
            }
          });
        case 4:
          return e.abrupt("return", e.sent.data.objects);
        case 5:
        case "end":
          return e.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function a() {
  return (a = r(t().mark((function e(r) {
    var o, c, a = arguments;
    return t().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = a.length > 1 && void 0 !== a[1] ? a[1] : {}, c = o.timeoutRetry, e.next = 3, (0, n.nikeApiInst)({
            timeoutRetry: c
          }).post("".concat(i, "/store_service_interactions/v1"), r);
        case 3:
          return e.abrupt("return", e.sent);
        case 4:
        case "end":
          return e.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function s() {
  return (s = r(t().mark((function e(r) {
    var o, c, a = arguments;
    return t().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = a.length > 1 && void 0 !== a[1] ? a[1] : {}, c = o.timeoutRetry, e.next = 3, (0, n.nikeApiInst)({
            timeoutRetry: c
          }).get("".concat(i, "/store_service_interactions/v1/").concat(r));
        case 3:
          return e.abrupt("return", e.sent);
        case 4:
        case "end":
          return e.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function u() {
  return (u = r(t().mark((function r(o) {
    var c, a, s = arguments;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return c = s.length > 1 && void 0 !== s[1] ? s[1] : {}, a = c.timeoutRetry, t.next = 3, (0, n.nikeApiInst)({
            timeoutRetry: a
          }).put("".concat(i, "/store_service_interactions/v1/").concat(o.id), e(e({}, o), {}, {
            status: "CANCELLED_CONSUMER"
          }));
        case 3:
          return t.abrupt("return", t.sent);
        case 4:
        case "end":
          return t.stop()
      }
    }), r)
  })))).apply(this, arguments)
}