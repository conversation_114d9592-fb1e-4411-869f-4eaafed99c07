.btn-position {
    height: 40px;
    overflow: hidden;
    position: fixed;
    width: 130px;
    z-index: 10000
}

.appBackBtn-wrapper {
    background: hsla(0,0%,7%,.25);
    border: 1px solid hsla(0,0%,100%,.15);
    border-radius: 100px;
    box-sizing: border-box;
    color: #fff;
    height: 40px;
    position: relative
}

.appBackBtn-wrapper.g-flex-center .icon-left-arrow {
    font-size: 14px;
    left: 5px;
    position: absolute
}

.appBackBtn-wrapper.g-flex-center .icon-close {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    font-size: 14px;
    height: 40px;
    padding-left: 8px;
    padding-right: 8px;
    position: absolute;
    right: 10px
}

.appBackBtn-wrapper.g-flex-center .back-button {
    -webkit-align-items: center;
    align-items: center;
    background: transparent;
    border-radius: unset;
    border-right: 1px solid hsla(0,0%,100%,.15);
    color: inherit;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    height: 28px;
    -webkit-justify-content: center;
    justify-content: center;
    left: 20px;
    margin: inherit;
    max-width: 75px;
    min-width: 48px;
    overflow: hidden;
    padding: inherit;
    padding-left: 5px;
    padding-right: 10px;
    position: absolute;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap
}
