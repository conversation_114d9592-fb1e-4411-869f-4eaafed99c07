var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var r = require("../state/global-components"),
  a = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../utils/configureStore")),
  o = require("../utils/coupons"),
  n = require("../utils/pageTransition"),
  i = require("../utils/global-components-config");
exports.default = Behavior({
  methods: {
    initiateWidgetAction: function(s, u, c) {
      var l = arguments,
        d = this;
      return t(e().mark((function p() {
        var f, m, b, O, h, g;
        return e().wrap((function(p) {
          for (;;) switch (p.prev = p.next) {
            case 0:
              f = !(l.length > 3 && void 0 !== l[3]) || l[3], m = s.widgetName, b = s.widgetAttributes, O = b.stockid, h = b.redirection, g = b["promo-code"], p.t0 = m, p.next = "claim" === p.t0 ? 5 : "copy" === p.t0 ? 10 : 11;
              break;
            case 5:
              if (p.t1 = O, !p.t1) {
                p.next = 9;
                break
              }
              return p.next = 9, d.triggerEvent("startLoadingTask", {
                showScreenMask: !1,
                fn: function() {
                  var r = t(e().mark((function t() {
                    return e().wrap((function(e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          return e.next = 2, (0, o.claimCoupon)({
                            stockId: O,
                            redirection: h,
                            customeClaimSucceedCb: u,
                            customeClaimFailedCb: c,
                            allowClaimSucceedFlow: f
                          });
                        case 2:
                        case "end":
                          return e.stop()
                      }
                    }), t)
                  })));
                  return function() {
                    return r.apply(this, arguments)
                  }
                }(),
                timeout: 6e4
              }, {
                bubbles: !0,
                composed: !0
              });
            case 9:
              return p.abrupt("break", 11);
            case 10:
              g && h ? wx.setClipboardData({
                data: g,
                success: function() {
                  wx.hideToast(), a.default.dispatch((0, r.showToast)(i.TOAST_COPIED_PROMO_CODE_CONF)), setTimeout((function() {
                    (0, n.goToPageWithInternalUrl)(h)
                  }), i.TOAST_COPIED_PROMO_CODE_CONF.duration - 200)
                }
              }) : h ? (0, n.goToPageWithInternalUrl)(h) : g && wx.setClipboardData({
                data: g,
                success: function() {
                  wx.hideToast(), a.default.dispatch((0, r.showToast)(i.TOAST_COPIED_PROMO_CODE_CONF))
                }
              });
            case 11:
            case "end":
              return p.stop()
          }
        }), p)
      })))()
    }
  }
});