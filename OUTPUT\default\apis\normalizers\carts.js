var e = require("../../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var r = require("../../vendor/date-fns"),
  t = require("../../utils/analytics/reporters"),
  i = require("../../utils/product"),
  a = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../../lib/dot-prop")),
  o = require("./request/carts"),
  u = require("./response/carts");
var n = function(e, t) {
  var i = !1,
    o = !1;
  if (t.availableGtins) {
    var u = t.availableGtins,
      n = t.skus.find((function(r) {
        return r.id === e
      }));
    if (n) {
      var s = u.find((function(e) {
        return e.gtin === n.gtin
      }));
      (i = s && s.available && !(0, r.isFuture)(a.default.get(t, "merchProduct.commerceStartDate"))) || (o = Boolean(t.availableGtins.find((function(e) {
        return e.available
      }))))
    }
  }
  return {
    available: i,
    isOtherSKUsAvailable: o
  }
};
exports.default = {
  apiRequ1: [o.normalizeGetMerchSkusByIdsRequestBody],
  response: [function() {
    var o = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
      u = o.skuList,
      s = void 0 === u ? [] : u,
      l = o.productFeeds,
      d = void 0 === l ? [] : l,
      c = {},
      v = !1,
      p = 0;
    return s.forEach((function(o) {
      var u = d.find((function(e) {
        return a.default.get(e, "productInfo.0.skus", []).find((function(e) {
          return e.id === o
        }))
      }));
      if (u) {
        var s = u.productInfo,
          l = s[0],
          f = l.productContent,
          m = l.skus,
          b = l.merchProduct,
          h = l.availableGtins,
          C = n(o, s[0]),
          q = C.available,
          y = C.isOtherSKUsAvailable,
          S = null == u ? void 0 : u.publishedContent,
          R = (null == S ? void 0 : S.properties).productCard;
        f || (p += 1, v = !0);
        var k = (0, i.getProductFuturePrice)(u);
        c[o] = {
          id: o,
          product: {
            id: b.id,
            styleColor: b.styleColor,
            imageSet: {
              images: [{
                view: b.styleColor.replace("-", "_"),
                company: "DotCom"
              }]
            },
            content: f,
            skus: m.map((function(r) {
              var i;
              return h ? i = h.find((function(e) {
                return e.gtin === r.gtin
              })) : (0, t.reportCustomError)("wechat_client_reported_skus_error", {
                message: "Field availableSkus is undefined",
                from: "cart",
                value: "StyleColor:".concat(b.styleColor, ", sku: ").concat(r)
              }), i || (i = {
                available: !1,
                level: "OOS"
              }), e(e({}, r), {}, {
                availability: {
                  available: i.available,
                  level: i.level
                }
              })
            })),
            productType: b.productType,
            quantityLimit: b.quantityLimit,
            isComingSoon: (0, r.isFuture)(b.commerceStartDate),
            squarishURL: R.properties.squarishURL,
            futurePrice: k
          },
          available: q,
          isOtherSKUsAvailable: y
        }
      }
    })), {
      skusMap: c,
      hasProductSoldOut: v,
      itemQuantitySoldOut: p
    }
  }],
  normalizeCartReviewResponse: u.normalizeCartReviewResponse,
  normalizeCartPromotionReviewResponse: u.normalizeCartPromotionReviewResponse,
  normalizeMerchReq: function(e) {
    return this.apiRequ1.reduce((function(e, r) {
      return r(e)
    }), e).skuIds
  },
  normalizeGetCartSKUs: function(e) {
    var r = e.skuList,
      t = e.productFeeds,
      i = e.productPublishedContents;
    return this.response.reduce((function(e, r) {
      return r(e)
    }), {
      skuList: r,
      productFeeds: t,
      productPublishedContents: i
    })
  }
};