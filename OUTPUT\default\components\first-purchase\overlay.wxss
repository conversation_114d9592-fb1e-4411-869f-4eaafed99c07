.container {
    -webkit-align-items: center;
    align-items: center;
    -webkit-animation-delay: .2s;
    animation-delay: .2s;
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    background-color: rgba(0,0,0,.8);
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 100vh;
    -webkit-justify-content: center;
    justify-content: center;
    left: 0;
    opacity: 0;
    position: fixed;
    top: 0;
    transition: background-color .5s;
    width: 100%;
    z-index: 10000
}

.container.fade-out {
    -webkit-animation-delay: .5s;
    animation-delay: .5s;
    -webkit-animation-direction: normal;
    animation-direction: normal;
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
    -webkit-animation-timing-function: cubic-bezier(.42,0,58,1);
    animation-timing-function: cubic-bezier(.42,0,58,1);
    opacity: 1
}

.container.new-member {
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .4s;
    animation-duration: .4s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    background-color: #fff;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    opacity: 0
}

.container.new-member .place-holder1 {
    -webkit-flex-basis: 12.56%;
    flex-basis: 12.56%
}

.container.new-member .place-holder2 {
    -webkit-flex-basis: 10.5%;
    flex-basis: 10.5%
}

.container.new-member .bottom {
    color: #9a9a9a
}

.container.new-member .bottom .skip-link {
    border-bottom: 1px solid #9a9a9a
}

.container.new-member .background-image {
    -webkit-animation-delay: 1.1s;
    animation-delay: 1.1s;
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .4s;
    animation-duration: .4s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 0
}

.container.new-member .header .logo,.container.new-member .header .subtitle,.container.new-member .header .title {
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .4s;
    animation-duration: .4s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: slide-top;
    animation-name: slide-top;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 0
}

.container.new-member .header .logo {
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s
}

.container.new-member .header .subtitle,.container.new-member .header .title {
    -webkit-animation-delay: 1.3s;
    animation-delay: 1.3s
}

.container.new-member .header .subtitle {
    -webkit-animation-duration: .6s;
    animation-duration: .6s
}

.container.new-member .bottom,.container.new-member .main {
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: slide-top;
    animation-name: slide-top;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 0
}

.container.new-member .main {
    -webkit-animation-delay: 1.5s;
    animation-delay: 1.5s
}

.container.new-member .bottom {
    -webkit-animation-delay: 1.6s;
    animation-delay: 1.6s
}

.container .background-image {
    bottom: 29.18%;
    left: 0;
    position: absolute;
    z-index: -10
}

.container .header {
    -webkit-align-self: flex-start;
    align-self: flex-start;
    margin-left: 60rpx
}

.container .header .logo,.container .header .subtitle,.container .header .title {
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: slide-top-70;
    animation-name: slide-top-70;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 0
}

.container .header .logo {
    -webkit-animation-delay: .1s;
    animation-delay: .1s;
    height: 46rpx;
    width: 120rpx
}

.container .header .title {
    -webkit-animation-delay: .2s;
    animation-delay: .2s;
    font-weight: 900;
    margin-top: 28rpx
}

.container .header .subtitle {
    -webkit-animation-delay: .3s;
    animation-delay: .3s;
    margin-top: 20rpx
}

.container .place-holder1 {
    -webkit-flex-basis: 10.1%;
    flex-basis: 10.1%
}

.container .bottom,.container .main {
    -webkit-animation-direction: forward;
    animation-direction: forward;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-name: slide-top-50;
    animation-name: slide-top-50;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 0
}

.container .main {
    -webkit-animation-delay: .4s;
    animation-delay: .4s;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px 0 rgba(0,0,0,.1);
    margin-bottom: 34rpx;
    overflow: hidden;
    padding-top: 74rpx;
    text-align: center;
    width: calc(100% - 120rpx)
}

.container .main .subtitle {
    letter-spacing: 3px
}

.container .main .title {
    margin-top: 24rpx
}

.container .main .cta {
    margin-top: 76rpx
}

.container .main .cta .btn {
    width: 87.3%
}

.container .bottom {
    -webkit-animation-delay: .5s;
    animation-delay: .5s;
    color: #fff;
    line-height: 18px
}

.container .bottom .skip-link {
    border-bottom: 1px solid #fff
}

@-webkit-keyframes slide-top {
    from {
        opacity: 0;
        -webkit-transform: translateY(43px);
        transform: translateY(43px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes slide-top {
    from {
        opacity: 0;
        -webkit-transform: translateY(43px);
        transform: translateY(43px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes slide-top-50 {
    from {
        opacity: 0;
        -webkit-transform: translateY(50px);
        transform: translateY(50px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes slide-top-50 {
    from {
        opacity: 0;
        -webkit-transform: translateY(50px);
        transform: translateY(50px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes slide-top-70 {
    from {
        opacity: 0;
        -webkit-transform: translateY(70px);
        transform: translateY(70px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes slide-top-70 {
    from {
        opacity: 0;
        -webkit-transform: translateY(70px);
        transform: translateY(70px)
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@-webkit-keyframes fadeOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes fadeOut {
    from {
        opacity: 1
    }

    to {
        opacity: 0
    }
}