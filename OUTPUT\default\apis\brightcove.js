Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.getVideoById = o;
var t = require("./common"),
  e = require("../utils/constants");

function o(o) {
  var i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    r = i.timeoutRetry,
    n = i.timeoutInMilliseconds,
    a = getApp().globalData.appConfig.VIDEO_API_PK;
  return (0, t.nikeApiInst)({
    timeoutRetry: r
  }).get("https://edge.api.brightcove.com/playback/v1/accounts/".concat(e.VIDEO_API_ACCOUNT, "/videos/").concat(o), {
    header: {
      "BCOV-Policy": a
    },
    timeout: n
  }).then((function(t) {
    return t.data
  }))
}
exports.default = o;