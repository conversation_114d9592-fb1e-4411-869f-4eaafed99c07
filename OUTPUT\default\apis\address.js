Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.addAddress = function(t, n, i, o, r) {
  var a = r.timeoutRetry,
    s = void 0 === a || a,
    c = getApp().globalData.appConfig.nikeApiBaseurl,
    u = "".concat(c, "/identity/user/v1/").concat(n, "/address");
  return (0, e.nikeApiInst)({
    timeoutRetry: s
  }).post(u, o, {
    header: {
      "Content-Type": "application/json",
      Authorization: "Bearer ".concat(t),
      "x-nike-ux-id": i
    }
  })
}, exports.getCnAddressInfo = function() {
  return e.wechatApiInst.get("https://idngeo.idn-prod.nikecloud.com.cn/profile/services/geo/countries/CN/country", {
    header: {
      "Content-Locale": "zh_CN"
    }
  })
}, exports.getUserAddressList = function(t, n, i, o) {
  var r = o.timeoutRetry,
    a = void 0 === r || r,
    s = getApp().globalData.appConfig.nikeApiBaseurl,
    c = "".concat(s, "/identity/user/v1/").concat(n, "/address");
  return (0, e.nikeApiInst)({
    timeoutRetry: a
  }).get(c, {
    header: {
      Authorization: "Bearer ".concat(t),
      "x-nike-ux-id": i
    }
  })
}, exports.removeAddress = function(t, n, i, o, r) {
  var a = r.timeoutRetry,
    s = void 0 === a || a,
    c = getApp().globalData.appConfig.nikeApiBaseurl,
    u = "".concat(c, "/identity/user/v1/").concat(n, "/address/").concat(o);
  return (0, e.nikeApiInst)({
    timeoutRetry: s
  }).delete(u, {
    header: {
      Authorization: "Bearer ".concat(t),
      "x-nike-ux-id": i,
      "Content-Type": "text/plain"
    }
  }).then((function(e) {
    return e
  }))
}, exports.updateAddress = function(t, n, i, o, r, a) {
  var s = a.timeoutRetry,
    c = void 0 === s || s,
    u = getApp().globalData.appConfig.nikeApiBaseurl,
    p = "".concat(u, "/identity/user/v1/").concat(n, "/address/").concat(o);
  return (0, e.nikeApiInst)({
    timeoutRetry: c
  }).put(p, r, {
    header: {
      "Content-Type": "application/json",
      Authorization: "Bearer ".concat(t),
      "x-nike-ux-id": i
    }
  }).then((function(e) {
    return e
  }))
};
var e = require("./common");