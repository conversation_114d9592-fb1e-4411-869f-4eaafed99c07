var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/objectSpread2"),
  r = require("../@babel/runtime/helpers/toConsumableArray"),
  n = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.getMerchProductContent = function(e) {
  var t = "/merch/contents/v1/".concat(e, "/content?country=CN&locale=zh_CN");
  return (0, c.nikeApiInst)().get(t)
}, exports.getMerchProductImages = function(e) {
  var t = "/merch/contents/v1/".concat(e, "/images?country=CN");
  return (0, c.nikeApiInst)().get(t)
}, exports.getMerchProductPrices = function(e) {
  var t = "/merch/prices/v2?".concat(["filter=country(CN)"].concat(r(e)).join("&"));
  return (0, c.nikeApiInst)().get(t)
}, exports.getMerchProducts = function(e) {
  var t = "/merch/products/v3/productId/".concat(e);
  return (0, c.nikeApiInst)().get(t)
}, exports.getMerchSkus = d, exports.getMerchSkusByIds = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
  return d(["filter=id(".concat(e.join(), ")")])
}, exports.getProductAvailability = function(e) {
  var t = "/deliver/available_gtins/v3?".concat(["filter=method(INSTORE)"].concat(r(e)).join("&"));
  return (0, c.nikeApiInst)().get(t)
}, exports.getProductImageByStyleColors = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
  return p(["filter=publishedContent.properties.products.styleColor(".concat(e.join(), ")"), "fields=".concat(["publishedContent.properties.productCard", "publishedContent.properties.products"].join(","))], {
    direct: !1
  })
}, exports.getRollupProducts = u, exports.getThread = p, exports.getThreadByIds = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
  return p(["filter=productInfo.merchProduct.id(".concat(e.join(), ")"), "filter=productInfo.merchProduct.status(ACTIVE)"], {
    direct: 1 === e.length
  })
}, exports.getThreadByStyleColors = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
  return p(["filter=publishedContent.properties.products.styleColor(".concat(e.join(), ")"), "filter=productInfo.merchProduct.status(ACTIVE)"], {
    direct: 1 === e.length
  })
};
var o = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs")),
  c = require("./common"),
  i = require("../utils/constants"),
  a = require("../utils/dataloader");

function u() {
  return s.apply(this, arguments)
}

function s() {
  return (s = n(e().mark((function r() {
    var n, u, s, p, l, d, h, f, v, g, m = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return n = m.length > 0 && void 0 !== m[0] ? m[0] : {}, u = n.filter, s = void 0 === u ? {} : u, p = n.query, l = void 0 === p ? {} : p, d = n.timeoutRetry, h = !1, e.prev = 2, e.next = 5, a.authLoader.load(a.LOADER_KEY);
        case 5:
          f = e.sent, h = f.swoosh, e.next = 11;
          break;
        case 9:
          e.prev = 9, e.t0 = e.catch(2);
        case 11:
          return v = t({
            marketplace: "CN",
            language: "zh-Hans",
            employeePrice: h
          }, s), g = o.default.stringify(t({
            filter: Object.keys(v).map((function(e) {
              return "".concat(e, "(").concat(v[e], ")")
            })),
            consumerChannelId: i.CHANNEL_ID_ROLLUP_THREAD,
            view: "SHOP",
            anchor: 0,
            count: 20
          }, l), {
            indices: !1
          }), e.abrupt("return", (0, c.nikeApiInst)({
            timeoutRetry: d
          }).get("/product_feed/rollup_threads/v2?".concat(g), {
            withoutToken: !0
          }));
        case 13:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [2, 9]
    ])
  })))).apply(this, arguments)
}

function p(e) {
  return l.apply(this, arguments)
}

function l() {
  return (l = n(e().mark((function t(n) {
    var o, u, s, p, l, d, h, f, v, g, m, C = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = C.length > 1 && void 0 !== C[1] ? C[1] : {}, u = o.includeNikeAppChannel, s = o.direct, p = void 0 === s || s, l = !1, e.prev = 2, e.next = 5, a.authLoader.load(a.LOADER_KEY);
        case 5:
          d = e.sent, l = d.swoosh, e.next = 11;
          break;
        case 9:
          e.prev = 9, e.t0 = e.catch(2);
        case 11:
          if (h = ["filter=marketplace(CN)", "filter=language(zh-Hans)", "filter=employeePrice(".concat(l, ")"), "filter=channelId(".concat(i.CHANNEL_ID_PRODUCT_THREAD, ")")], f = u ? "WeChat,NikeApp,Nike.com" : "WeChat", h.push("filter=productInfo.merchProduct.channels(".concat(f, ")")), v = [].concat(h, r(n)).join("&"), !p) {
            e.next = 17;
            break
          }
          return g = "/product_feed/threads/v3?".concat(v), e.abrupt("return", (0, c.nikeApiInst)().get(g));
        case 17:
          return m = "/product_feed/threads/v2?".concat(v), e.abrupt("return", c.wechatApiInst.get(m));
        case 19:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [2, 9]
    ])
  })))).apply(this, arguments)
}

function d(e) {
  var t = "/merch/skus/v2?".concat(["country=CN", "language=zh-CN"].concat(r(e)).join("&"));
  return (0, c.nikeApiInst)().get(t)
}
exports.default = u;