var e = require("../../@babel/runtime/helpers/objectSpread2");
Component({
  options: {
    addGlobalClass: !0
  },
  properties: {
    secondaryUrl: {
      type: String,
      value: void 0
    },
    url: {
      type: String
    },
    aspectRatio: {
      type: Number
    },
    key: {
      type: Number
    },
    width: {
      type: Number
    },
    withSwoosh: {
      type: Boolean
    },
    lazy: {
      type: Boolean
    },
    lazyLoadMargin: {
      type: Number
    },
    height: {
      type: Number
    },
    widthStr: {
      type: String
    },
    delayAppearance: {
      type: Number
    },
    mode: {
      type: String
    },
    adjustAspectRatio: {
      type: Boolean
    }
  },
  data: {
    isSecondaryLoaded: !1,
    isLoaded: !1,
    style: "",
    hide: "display:none"
  },
  methods: {
    handleImageLoaded: function(t) {
      var a = t.detail;
      this.triggerEvent("ImageLoaded", e({}, a));
      var d = a.imgHeight,
        i = a.imgWidth;
      this.setData({
        isLoaded: !0,
        style: "width:".concat(i, "px;height:").concat(d, "px; display:block;")
      })
    },
    handleSecondaryLoaded: function(e) {
      var t = e.detail,
        a = t.width,
        d = t.height;
      this.setData({
        isSecondaryLoaded: !0
      });
      var i = this.data,
        r = i.key,
        o = i.secondaryUrl;
      this.triggerEvent("SecondaryLoaded", {
        key: r,
        url: o,
        width: a,
        height: d
      })
    },
    handleSecondaryError: function(e) {},
    handleImageError: function(e) {}
  }
});