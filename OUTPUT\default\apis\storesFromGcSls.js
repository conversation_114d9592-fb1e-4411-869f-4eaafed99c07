var t = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getNearByStoresV3 = function(t) {
  return c.apply(this, arguments)
}, exports.getStoreByStoreNumberV2 = function(t) {
  return s.apply(this, arguments)
}, exports.getStoreByUidV2 = function(t) {
  return o.apply(this, arguments)
}, exports.getStoresByIdsV2 = function(t) {
  return u.apply(this, arguments)
}, exports.searchStoresV3 = function(t) {
  return a.apply(this, arguments)
};
var e = require("./common"),
  n = "/onemp";

function c() {
  return (c = r(t().mark((function r(c) {
    var o, a, u, s, i, p, h, f, l, v, d;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return o = c.radiusInKm, a = void 0 === o ? 0 : o, u = c.longitude, s = c.latitude, i = c.anchor, p = void 0 === i ? 0 : i, h = c.count, f = void 0 === h ? 50 : h, l = Math.floor(a) + 1, v = Number(u).toFixed(2), d = Number(s).toFixed(2), t.prev = 2, t.abrupt("return", e.wechatApiInst.get("".concat(n, "/stores/v3?radius=").concat(l, "&longitude=").concat(v, "&latitude=").concat(d, "&anchor=").concat(p, "&count=").concat(f)));
        case 6:
          return t.prev = 6, t.t0 = t.catch(2), t.abrupt("return", Promise.reject(t.t0));
        case 9:
        case "end":
          return t.stop()
      }
    }), r, null, [
      [2, 6]
    ])
  })))).apply(this, arguments)
}

function o() {
  return (o = r(t().mark((function r(c) {
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return t.prev = 0, t.abrupt("return", e.wechatApiInst.get("".concat(n, "/store/v2/").concat(c)));
        case 4:
          return t.prev = 4, t.t0 = t.catch(0), t.abrupt("return", Promise.reject(t.t0));
        case 7:
        case "end":
          return t.stop()
      }
    }), r, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function a() {
  return (a = r(t().mark((function r(c) {
    var o, a, u, s, i;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return o = c.value, a = c.anchor, u = void 0 === a ? 0 : a, s = c.count, i = void 0 === s ? 100 : s, t.abrupt("return", e.wechatApiInst.get("".concat(n, "/stores/v3?name=").concat(o, "&anchor=").concat(u, "&count=").concat(i)));
        case 2:
        case "end":
          return t.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function u() {
  return (u = r(t().mark((function r(c) {
    var o;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return o = c.join(","), t.abrupt("return", e.wechatApiInst.get("".concat(n, "/stores/v2?ids=").concat(o)));
        case 2:
        case "end":
          return t.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function s() {
  return (s = r(t().mark((function r(c) {
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return t.abrupt("return", e.wechatApiInst.get("".concat(n, "/stores/v2?storeNumber=").concat(c)));
        case 1:
        case "end":
          return t.stop()
      }
    }), r)
  })))).apply(this, arguments)
}