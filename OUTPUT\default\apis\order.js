var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/toConsumableArray"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getAllUserOrderList = function(e, r) {
  return c.apply(this, arguments)
}, exports.getGuestOrder = function(e, r) {
  var t = "/order_mgmt/user_order_details/v2/".concat(e, "?filter=phoneNumber(").concat(r, ")");
  return (0, i.nikeApiInst)({
    usingWxCloud: !0
  }).get(t, {
    header: {
      "x-nike-visitid": "7",
      "x-nike-visitorid": (0, n.uuid)(),
      appid: "orders"
    }
  })
}, exports.getGuestOrderShipment = function(e, r, t) {
  var o = t ? "".concat(u.NIKE_API_BASE_URL, "/ship/user_shipments/v1?locale=zh_CN&filter=orderNumber(").concat(e, ")&filter=email(").concat(t, ")") : "".concat(u.NIKE_API_BASE_URL, "/ship/user_shipments/v1?locale=zh_CN&filter=orderNumber(").concat(e, ")&filter=phoneNumber(").concat(r, ")");
  return (0, i.nikeApiInst)({}).get(o, {
    header: {
      "x-nike-visitid": "7",
      "x-nike-visitorid": (0, n.uuid)(),
      appid: "orders"
    },
    timeout: 5e3
  })
}, exports.getUserOrderDetail = function(e) {
  var r = "/order_mgmt/user_order_details/v2/".concat(e);
  return (0, i.nikeApiInst)({
    timeoutRetry: !1,
    usingWxCloud: !0
  }).get(r, {
    timeout: 5e3
  })
}, exports.getUserOrderList = function(e, r) {
  var t = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
    n = t.timeoutRetry,
    u = void 0 === n || n,
    a = t.count,
    s = void 0 === a ? 100 : a,
    c = t.anchor,
    d = void 0 === c ? 0 : c,
    m = t.filter,
    p = void 0 === m ? {} : m,
    l = "/order_mgmt/user_order_summary/v2?".concat(o.default.stringify({
      filter: Object.keys(p).filter((function(e) {
        return p[e]
      })).map((function(e) {
        return "".concat(e, "(").concat(p[e], ")")
      })),
      sort: "orderSubmitDateDesc",
      count: s,
      anchor: d
    }, {
      indices: !1
    }));
  return (0, i.nikeApiInst)({
    timeoutRetry: u,
    usingWxCloud: !0
  }).get(l, {
    header: {
      Authorization: "Bearer ".concat(e),
      upmId: r
    }
  })
}, exports.getUserOrderShipment = function(e) {
  var r = e.orderNumber,
    t = "".concat(u.NIKE_API_BASE_URL, "/ship/user_shipments/v1"),
    n = ["filter=orderNumber(".concat(r, ")")].join("&");
  return (0, i.nikeApiInst)({
    timeoutRetry: !1
  }).get("".concat(t, "?").concat(n), {
    timeout: 5e3
  })
}, exports.postCancelOrder = function(e) {
  var r = e.upmId,
    t = e.orderNumber,
    n = e.accessToken,
    o = e.isGuest,
    s = e.userInfo,
    c = "".concat(u.NIKE_API_BASE_URL, "/order_mgmt/ordermods/v1/").concat(t, "/cancelOrder"),
    d = o ? {
      request: {
        userInfo: s
      }
    } : {
      request: {}
    };
  return (0, i.nikeApiInst)().post(c, d, {
    header: o ? {} : {
      Authorization: "Bearer ".concat(n),
      upmId: r
    }
  }).then((function(e) {
    var t = a.default.get(e, "data.id");
    return (0, i.callAsyncJob)("".concat(u.NIKE_API_BASE_URL, "/order_mgmt/ordermods/v1/jobs/").concat(t), {
      header: o ? {} : {
        Authorization: "Bearer ".concat(n),
        upmId: r
      }
    })
  }))
};
var n = require("../utils/common"),
  o = s(require("../vendor/qs")),
  i = require("./common"),
  u = require("./constants"),
  a = s(require("../lib/dot-prop"));

function s(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}

function c() {
  return (c = t(e().mark((function t(n, o) {
    var u, s, c, d, m, p, l, _ = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          u = _.length > 2 && void 0 !== _[2] ? _[2] : {}, s = u.timeoutRetry, c = void 0 === s || s, d = u.fields, m = "/order_mgmt/user_order_summary/v2?sort=orderSubmitDateDesc&count=1000&anchor=0&fields=".concat((void 0 === d ? [] : d).join(",")), p = [];
        case 2:
          if (!m) {
            e.next = 9;
            break
          }
          return e.next = 5, (0, i.nikeApiInst)({
            timeoutRetry: c,
            usingWxCloud: !0
          }).get(m, {
            header: {
              Authorization: "Bearer ".concat(n),
              upmId: o
            }
          });
        case 5:
          l = e.sent, p = [].concat(r(p), r(a.default.get(l, "data.objects", []))), m = a.default.get(l, "data.pages.next");
        case 7:
          e.next = 2;
          break;
        case 9:
          return e.abrupt("return", p);
        case 10:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}