var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getStoreByNumber = function(e) {
  return i.apply(this, arguments)
}, exports.getStoreByUid = function(e) {
  return a.apply(this, arguments)
}, exports.getStoreByUids = function(e) {
  return o.apply(this, arguments)
}, exports.searchStores = function(e) {
  return c.apply(this, arguments)
};
var r = require("./common"),
  n = "/store/store_locations";

function a() {
  return (a = t(e().mark((function t(a) {
    var o, i, c, u = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = u.length > 1 && void 0 !== u[1] ? u[1] : {}, i = o.timeoutRetry, c = getApp().globalData.appConfig.nikeApiCallerId, e.next = 4, (0, r.nikeApiInst)({
            timeoutRetry: i
          }).get("".concat(n, "/v1/").concat(a), {
            header: {
              "nike-api-caller-id": c
            }
          });
        case 4:
          return e.abrupt("return", e.sent);
        case 5:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}

function o() {
  return (o = t(e().mark((function t(a) {
    var o, i, c, u, p = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = p.length > 1 && void 0 !== p[1] ? p[1] : {}, i = o.timeoutRetry, c = getApp().globalData.appConfig.nikeApiCallerId, u = a.join(","), e.next = 4, (0, r.nikeApiInst)({
            timeoutRetry: i
          }).get("".concat(n, "/v1?filter=ids(").concat(u, ")"), {
            header: {
              "nike-api-caller-id": c
            }
          });
        case 4:
          return e.abrupt("return", e.sent);
        case 5:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}

function i() {
  return (i = t(e().mark((function t(a) {
    var o, i, c, u, p, s = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = s.length > 1 && void 0 !== s[1] ? s[1] : {}, i = o.timeoutRetry, c = (Array.isArray(a) ? a : [a]).map((function(e) {
            return "storeNumber==".concat(e)
          })).join(" or "), u = getApp().globalData.appConfig.nikeApiCallerId, p = "((".concat(c, ") and (storeStatus==CLOSED or storeStatus==OPEN or storeStatus==UNOPENED))"), e.next = 4, (0, r.nikeApiInst)({
            timeoutRetry: i
          }).get("".concat(n, "/v1?search=").concat(encodeURIComponent(p)), {
            header: {
              "nike-api-caller-id": u
            }
          });
        case 4:
          return e.abrupt("return", e.sent);
        case 5:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}

function c() {
  return (c = t(e().mark((function t(a) {
    var o, i, c, u, p, s, l = arguments;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = l.length > 1 && void 0 !== l[1] ? l[1] : 0, i = l.length > 2 && void 0 !== l[2] ? l[2] : 500, c = l.length > 3 && void 0 !== l[3] ? l[3] : {}, u = c.timeoutRetry, p = getApp().globalData.appConfig.nikeApiCallerId, s = "((name=='".concat(a, "' or address.address1=='").concat(a, "') and (facilityType=='NIKE_OWNED_STORE' or facilityType == 'MONO_BRAND_NON_FRANCHISEE_PARTNER_STORE' or facilityType=='FRANCHISEE_PARTNER_STORE'))"), e.next = 6, (0, r.nikeApiInst)({
            timeoutRetry: u
          }).get("".concat(n, "/v1?language=zh&search=").concat(s, "&anchor=").concat(o, "&count=").concat(i), {
            header: {
              "nike-api-caller-id": p
            }
          });
        case 6:
          return e.abrupt("return", e.sent);
        case 7:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}