Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.createJobForDeferredWechatPayment = function(r, n) {
  var a = t(r);
  return (0, e.nikeApiInst)().post("/payment/deferred_wechat_payments/v2", n, {
    header: a
  })
}, exports.getDeferredWechatPaymentJob = function(r, n) {
  var a = "/payment/deferred_wechat_payments/v2/jobs/".concat(n),
    o = t(r);
  return (0, e.nikeApiInst)({
    timeoutRetry: !0
  }).get(a, {
    header: o
  })
};
var e = require("./common");

function t(e) {
  var t = {};
  return "string" == typeof e && "" !== e && (t["X-B3-TraceId"] = e), t
}