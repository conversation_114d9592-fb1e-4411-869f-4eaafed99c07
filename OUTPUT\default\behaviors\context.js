var t = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("../utils/data");
exports.default = Behavior({
  properties: {
    alias: {
      type: String,
      value: ""
    }
  },
  definitionFilter: function(e) {
    function n() {
      var e = this.__contextdata__ || {},
        n = this.properties.alias;
      return t({
        alias: n
      }, e)
    }
    var o = "function" == typeof e.methods.collectContext ? e.methods.collectContext : function() {
      return {}
    };
    e.methods.collectContext = function() {
      return t(t({}, n.call(this)), o.call(this))
    }
  },
  methods: {
    getContextData: function() {
      var t = this;
      return new Promise((function(e) {
        var n = setTimeout((function() {
          e({})
        }), 20);
        t.triggerTraversalEvent("getContextData", null, (function(t) {
          clearTimeout(n), e(t)
        }))
      }))
    },
    onTraversalEvent: function(t) {
      var n = this.collectContext();
      t.detail && t.detail.contexts && (0, e.isNonEmptyObject)(n) && t.detail.contexts.unshift(n)
    },
    provideContext: function() {
      var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
      this.__contextdata__ = t(t({}, this.__contextdata__ || {}), e)
    },
    triggerTraversalEvent: function(t, n, o) {
      var i = this.collectContext(),
        r = [];
      (0, e.isNonEmptyObject)(i) && r.push(i), this.triggerEvent("traversal", {
        event: t,
        detail: n,
        callback: o,
        contexts: r
      }, {
        bubbles: !0,
        composed: !0
      })
    }
  }
});