var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var t = require("../utils/analytics/core"),
  s = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../utils/configureStore")),
  n = require("../state/app");
exports.default = Behavior({
  data: {
    exposureOption: {
      properties: {}
    }
  },
  lifetimes: {
    attached: function() {
      var t = this;
      return r(e().mark((function r() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return t.addSubscribe(), e.next = 3, t.setExposureOption();
            case 3:
            case "end":
              return e.stop()
          }
        }), r)
      })))()
    }
  },
  pageLifetimes: {
    show: function() {
      var t = this;
      return r(e().mark((function r() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return t.addSubscribe(), e.next = 3, t.setExposureOption();
            case 3:
            case "end":
              return e.stop()
          }
        }), r)
      })))()
    },
    hide: function() {
      this.removeSubscribe()
    }
  },
  methods: {
    removeSubscribe: function() {
      this.unsubscribe && (this.unsubscribe(), this.unsubscribe = null)
    },
    addSubscribe: function() {
      var t = this;
      this.unsubscribe || (this.unsubscribe = s.default.subscribe(r(e().mark((function r() {
        var i, u;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (i = s.default.getState(), u = i.latestAction.type, e.t0 = n.SET_TRUST_LINK === u, !e.t0) {
                e.next = 5;
                break
              }
              return e.next = 5, t.setExposureOption();
            case 5:
            case "end":
              return e.stop()
          }
        }), r)
      })))))
    },
    setExposureOption: function() {
      var s = this;
      return r(e().mark((function r() {
        var n, i;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.next = 2, (0, t.getTrustLinkProperties)();
            case 2:
              n = e.sent, i = s.data.exposureOption.properties, JSON.stringify(i) !== JSON.stringify(n) && s.setData({
                exposureOption: {
                  properties: n
                }
              }, (function() {
                s.bindExposureObserver ? s.bindExposureObserver() : s.data.sensorsExposureClass && getApp().globalData.exposure.addObserverByClassName(s.data.sensorsExposureClass, {
                  component: s
                })
              }));
            case 5:
            case "end":
              return e.stop()
          }
        }), r)
      })))()
    }
  }
});