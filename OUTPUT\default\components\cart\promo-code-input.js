var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../../@babel/runtime/helpers/asyncToGenerator"),
  o = require("../../utils/constants"),
  t = require("../../vendor/redux"),
  n = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../../utils/configureStore")),
  a = require("../../lib/mp-redux/index"),
  s = require("../../constants/localization"),
  i = require("../../state/global-components"),
  d = require("../../state/cart/selectors"),
  u = require("../../state/cart/index"),
  c = require("../../state/cart/actions"),
  p = require("../../utils/global-components-config"),
  l = require("../../state/user"),
  m = require("../../utils/analytics/core"),
  C = require("../../utils/dataloader");
var P = {
    options: {
      addGlobalClass: !0
    },
    data: {
      promoCode: ""
    },
    lifetimes: {
      ready: function() {
        this.triggerEvent("resizePanel", {}, {
          bubbles: !0,
          composed: !0
        })
      },
      attached: function() {}
    },
    methods: {
      removeErrorMessage: function() {
        this.data.promoCodeInputError && this.resetPromoCodeErrorState()
      },
      handleTapPasteFromClipboard: function() {
        var t, n = this;
        this.isProcessingPromo || (this.isProcessingPromo = !0, (0, m.sendTrackEvent)("Cart_Add_Promo_Paste"), this.triggerEvent("startLoadingTask", {
          fn: (t = r(e().mark((function t() {
            return e().wrap((function(t) {
              for (;;) switch (t.prev = t.next) {
                case 0:
                  return t.next = 2, new Promise((function(t) {
                    var a;
                    wx.getClipboardData({
                      success: (a = r(e().mark((function o(a) {
                        var i;
                        return e().wrap((function(o) {
                          for (;;) switch (o.prev = o.next) {
                            case 0:
                              (i = a.data) && 0 !== i.trim().length ? (n.previousValue = i, n.setData({
                                promoCodeDisplayValue: i.toUpperCase()
                              }, r(e().mark((function r() {
                                return e().wrap((function(e) {
                                  for (;;) switch (e.prev = e.next) {
                                    case 0:
                                      return n.savePromoCodeInput(n.data.promoCodeDisplayValue), e.next = 3, n.doAddPromoCode(n.data.promoCodeDisplayValue);
                                    case 3:
                                      t();
                                    case 4:
                                    case "end":
                                      return e.stop()
                                  }
                                }), r)
                              }))))) : (n.showToast((0, p.getGeneralErrorToast)(s.CHECKOUT.EMPTY_CLIP)), t());
                            case 2:
                            case "end":
                              return o.stop()
                          }
                        }), o)
                      }))), function(e) {
                        return a.apply(this, arguments)
                      }),
                      fail: function(e) {
                        e.errno === o.EMPTY_CLIP_ERRNO && (n.showToast((0, p.getGeneralErrorToast)(s.CHECKOUT.EMPTY_CLIP)), t())
                      },
                      complete: function() {
                        n.isProcessingPromo = !1
                      }
                    })
                  }));
                case 2:
                case "end":
                  return t.stop()
              }
            }), t)
          }))), function() {
            return t.apply(this, arguments)
          })
        }, {
          bubbles: !0,
          composed: !0
        }))
      },
      handleApplyPromoCodeTap: function() {
        var o = this;
        return r(e().mark((function r() {
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return e.next = 2, o.doAddPromoCode(o.data.promoCodeInput);
              case 2:
              case "end":
                return e.stop()
            }
          }), r)
        })))()
      },
      handlePromoCodeChange: function(e) {
        var r = e.detail.value;
        this.clearPromoCodeInputError(), this.savePromoCodeInput(r), this.triggerEvent("resizePanel", {}, {
          bubbles: !0,
          composed: !0
        })
      },
      doAddPromoCode: function(o) {
        var t = this;
        return r(e().mark((function r() {
          var n, a, s;
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return n = o.toUpperCase(), e.next = 3, C.promoCodeLoader.load(C.LOADER_KEY);
              case 3:
                if (e.t1 = n, e.t0 = e.sent[e.t1], e.t0) {
                  e.next = 7;
                  break
                }
                e.t0 = n;
              case 7:
                return a = e.t0, e.prev = 8, e.next = 11, t.addPromoCode(a);
              case 11:
                (s = e.sent) === u.MESSAGE.PROMOTION_NOT_APPLIED.CODE || s === u.MESSAGE.MEMBERSHIP_REQUIRED.CODE ? t.showModal({
                  title: "优惠券不推荐",
                  detail: u.MESSAGE[s].MODAL_TEXT,
                  isShowingCloseBtn: !1,
                  isShowingCancelBtn: !1,
                  isShowingConfirmBtn: !0,
                  confirmText: "我知道了",
                  confirmHandlerName: "hideCartPromoInvalidModal"
                }) : t.showToast(p.TOAST_PROMO_CODE_APPLIED), (0, m.sendTrackEvent)("Cart_Add_Promo_Confirm", {
                  promoCode: o,
                  errorCode: "Valid"
                }), t.triggerEvent("hideCartPromoInput", {}, {
                  bubbles: !0,
                  composed: !0
                }), e.next = 18;
                break;
              case 15:
                e.prev = 15, e.t2 = e.catch(8), t.triggerEvent("resizePanel", {}, {
                  bubbles: !0,
                  composed: !0
                }), (0, m.sendTrackEvent)("Cart_Add_Promo_Confirm", {
                  promoCode: o,
                  errorCode: e.t2.message.toLowerCase().indexOf("expire") > -1 ? "Expired" : "Invalid"
                });
              case 18:
              case "end":
                return e.stop()
            }
          }), r, null, [
            [8, 15]
          ])
        })))()
      }
    }
  },
  h = {
    showToast: i.showToast,
    showModal: i.showModal,
    addPromoCode: c.addPromoCode,
    hideAddPromoCodeForm: c.hideAddPromoCodeForm,
    clearPromoCodeInputError: c.clearPromoCodeInputError,
    savePromoCodeInput: c.savePromoCodeInput
  };
(0, t.compose)(Component, (0, a.connectComponent)(n.default)((function(e) {
  var r = (0, d.getPromotionCodeUserInput)(e);
  return {
    userId: (0, l.getAuthUserId)(e),
    promoCodeInput: r,
    promoCodeInputError: (0, d.getPromoCodeInputError)(e),
    cartItemSelections: (0, d.getCartItemSelections)(e)
  }
}), h))(P);