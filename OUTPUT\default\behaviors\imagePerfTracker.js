Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../utils/configureStore")),
  t = require("../utils/analytics/reporters");
exports.default = Behavior({
  data: {
    loadStartMapping: {}
  },
  properties: {
    traceName: {
      type: String,
      value: "default"
    }
  },
  methods: {
    onLoadImageStart: function() {
      var e = this.data;
      e.loadStartMapping[e.src] = Date.now()
    },
    onLoadImageEnd: function() {
      var a = this.data,
        r = a.loadStartMapping,
        o = a.traceName,
        i = a.src,
        d = r[i];
      d && e.default.dispatch((0, t.reportPerfEvent)({
        type: t.PERF_TYPE.IMAGE_LOAD,
        route: i,
        perf: {
          loadTime: Date.now() - d
        },
        options: {
          traceName: o
        }
      }))
    }
  }
});