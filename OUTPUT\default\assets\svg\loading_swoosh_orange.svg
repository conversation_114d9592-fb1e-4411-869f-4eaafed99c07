<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40px" height="40px" viewBox="0 0 40 40" >
<defs>
    <path id="swooshPath" d="M5.1,13.4C2.5,16.5,0,20.3,0,23.1c0,1.1,0.3,2,1.1,2.7c1.2,1,2.5,1.4,3.8,1.4c1.9,0,3.8-0.8,5.2-1.3
    c2.5-1,29.7-12.8,29.7-12.8c0.3-0.1,0.2-0.3-0.1-0.2c-0.1,0-29.7,8-29.7,8c-0.6,0.2-1.2,0.2-1.7,0.2c-2.3,0-4.3-1.2-4.3-3.9
    C4.1,16.2,4.4,14.9,5.1,13.4L5.1,13.4z" fill="#434343"
    />
</defs>
<defs>
    <clipPath id="swooshPathClip">
        <use xlink:href="#swooshPath" />
    </clipPath>
</defs>
<use xlink:href="#swooshPath" />
<rect width="33" height="40" style="fill: #FF841F" clip-path="url(#swooshPathClip)">
  <animate attributeName="x" values="-40;80" keyTimes="0;1" dur=".6s" repeatCount="indefinite" calcMode="spline" keySplines="0.42 0 0.58 1"></animate>
</rect>
</svg>