var e = require("../../vendor/redux"),
  t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../../utils/configureStore")),
  a = require("../../state/app"),
  o = require("../../utils/analytics/reporters"),
  r = require("../../lib/mp-redux/index");
var n = {
    options: {
      addGlobalClass: !0
    },
    data: {
      appName: void 0,
      top: 120,
      left: 10,
      vWidth: 140,
      vHeight: 40,
      selfWidth: 140
    },
    attached: function() {
      var e, t = wx.getSystemInfoSync().screenWidth,
        a = 2 * this.data.appName.length - (this.data.appName.match(/[A-Za-z0-9]/g) || []).length;
      e = a <= 4 ? 112 : a > 8 ? 140 : 112 + 6 * (a - 4), this.setData({
        left: t - e - 10,
        selfWidth: e
      })
    },
    methods: {
      onTap: function(e) {},
      onError: function(e) {
        t.default.dispatch((0, o.reportError)(e, "Back to App"))
      },
      onClose: function() {
        this.closeAppBack()
      }
    }
  },
  p = {
    closeAppBack: a.closeAppBack
  };
(0, e.compose)(Component, (0, r.connectComponent)(t.default)((function(e) {
  return {
    isShow: (0, a.getIsShowBackApp)(e),
    appName: (0, a.getAppName)(e) || "回到APP"
  }
}), p))(n);