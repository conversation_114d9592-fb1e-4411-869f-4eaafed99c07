Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.STORED_PAYMENT_TYPE_GIFT_CARD = exports.CURRENCY_CNY = void 0, exports.addGiftCardToStoredPayments = function(t) {
  var n = t.cardNumber,
    i = t.cardPin,
    s = t.gcExpiryDate,
    d = void 0 === s ? "2099-12-31T00:00:50.000+0000" : s,
    a = t.isDefault,
    u = void 0 !== a && a,
    c = t.type,
    p = void 0 === c ? r : c,
    m = t.currency,
    y = {
      accountNumber: n,
      pin: i,
      gcExpiryDate: d,
      isDefault: u,
      type: p,
      currency: void 0 === m ? o : m
    };
  return (0, e.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).post("/commerce/storedpayments/consumer/savepayment", y)
}, exports.deleteGiftCardFromStoredPayments = function(t) {
  var r = t.paymentId;
  return (0, e.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).delete("/commerce/storedpayments/consumer/storedpayments/".concat(r))
}, exports.getStoredPayments = function(n) {
  var i = n.address,
    s = void 0 === i ? {} : i,
    d = n.includebalance,
    a = void 0 === d || d,
    u = n.validateshipping,
    c = void 0 !== u && u,
    p = n.type,
    m = void 0 === p ? r : p,
    y = n.currency,
    v = {
      includebalance: a,
      validateshipping: c,
      type: m,
      currency: void 0 === y ? o : y
    },
    l = s || {};
  return (0, e.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).post("/commerce/storedpayments/consumer/storedpayments?".concat(t.default.stringify(v)), l)
};
var e = require("./common"),
  t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs"));
var r = exports.STORED_PAYMENT_TYPE_GIFT_CARD = "GiftCard",
  o = exports.CURRENCY_CNY = "CNY";