Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.inquire = function(e, i) {
  return t.wechatApiInst.get("/notify_me/subscription/v1?filter=subjectType(".concat(e, ")&filter=subjectId(").concat(i, ")"))
}, exports.inquireV2 = function(e, i) {
  return t.wechatApiInst.get("/notify_me/subscription/v2?filter=subjectTypes(".concat(e.join(","), ")&filter=subjectIds(").concat(i.join(","), ")"))
}, exports.subscribe = function(e, i) {
  return t.wechatApiInst.post("/notify_me/subscription/v1", {
    subjectType: e,
    subjectId: i
  })
}, exports.subscribeV2 = function(e) {
  return t.wechatApiInst.post("/notify_me/subscription/v2", e)
};
var t = require("./common");