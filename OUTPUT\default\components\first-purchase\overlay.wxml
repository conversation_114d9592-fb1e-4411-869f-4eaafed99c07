<view class="container {{isNewMember?'new-member':''}} {{isFadingOut?'fade-out':''}}" wx:if="{{loggedIn&&isShow}}">
    <view class="background-image" wx:if="{{isNewMember}}">
        <image-loader src="{{threadContent.main.image}}" widthStr="750rpx"></image-loader>
    </view>
    <view class="header c-ff">
        <image class="logo" src="https://mp-static-assets.gc.nike.com/image/shop/home-logo-light.svg"></image>
        <view class="title h1">{{isNewMember?threadContent.title1:threadContent.title2}}</view>
        <view class="subtitle p3">{{threadContent.body}}</view>
    </view>
    <view class="place-holder1"></view>
    <view class="main">
        <view class="subtitle p3 c-9a">{{threadContent.main.subtitle}}</view>
        <view class="title h3">{{threadContent.main.title}}</view>
        <view class="cta">
            <button bind:tap="onClaim" class="btn" hoverClass="press-down-button">
                <image src="https://mp-static-assets.gc.nike.com/image/shop/icon_red_packet.svg" style="width:16px;height:14px;"></image>
                <text class="btn--txt" style="margin-left:8px;">{{threadContent.cta.text}}</text>
            </button>
        </view>
    </view>
    <view class="bottom p3">
        <text>{{threadContent.main.body}}</text>
        <text bind:tap="onSkip" class="skip-link">{{threadContent.skipText}}</text>
    </view>
    <view class="place-holder2"></view>
</view>
