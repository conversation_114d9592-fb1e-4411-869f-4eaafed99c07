Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.findEventsByLocation = function(n, i) {
  var a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
    o = a.timeoutRetry,
    r = getApp().globalData.appConfig,
    c = r.eventsApi,
    p = r.eventsApiClientId;
  return (0, e.nikeApiInst)({
    timeoutRetry: o
  }).get("".concat(c, "/v1/events?validate=false&eventGroup=location:").concat(i), {
    header: {
      "x-api-client-id": p,
      "x-api-authorization": "Bearer ".concat(n)
    }
  }).then((function(e) {
    return t.default.get(e, "data.body", {})
  }))
}, exports.findEventsByLocationWithFilter = function(n, i) {
  var a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
    o = a.timeoutRetry,
    r = getApp().globalData.appConfig,
    c = r.eventsApi,
    p = r.eventsApiClientId,
    u = new Date;
  u.setMonth(u.getMonth() - 3);
  var s = "startDateRange=after:".concat(u.getFullYear(), "-").concat(u.getMonth() + 1, "-").concat(u.getDate());
  return (0, e.nikeApiInst)({
    timeoutRetry: o
  }).get("".concat(c, "/v1/events?validate=false&eventGroup=location:").concat(i, "&sorting=startDate:desc&").concat(s), {
    header: {
      "x-api-client-id": p,
      "x-api-authorization": "Bearer ".concat(n)
    }
  }).then((function(e) {
    return t.default.get(e, "data.body", {})
  }))
}, exports.getEventById = function(n, i) {
  var a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
    o = a.timeoutRetry,
    r = getApp().globalData.appConfig,
    c = r.eventsApi,
    p = r.eventsApiClientId;
  return (0, e.nikeApiInst)({
    timeoutRetry: o
  }).get("".concat(c, "/v1/events/").concat(i, "?validate=false"), {
    header: {
      "x-api-client-id": p,
      "x-api-authorization": "Bearer ".concat(n)
    }
  }).then((function(e) {
    return t.default.get(e, "data.body", {})
  }))
}, exports.getEventsAccessToken = function() {
  var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    i = n.timeoutRetry,
    a = getApp().globalData.appConfig,
    o = a.eventsApi,
    r = a.eventsApiClientId,
    c = a.eventsApiAuth;
  return (0, e.nikeApiInst)({
    timeoutRetry: i
  }).post("".concat(o, "/v1/accessTokens"), {
    timeToLiveInMins: 60
  }, {
    header: {
      "content-type": "application/json",
      "x-api-client-id": r,
      "x-api-authorization": c
    }
  }).then((function(e) {
    return t.default.get(e, "data.body.token")
  }))
}, exports.getMyEvents = function(t, n) {
  var i = n.timeoutRetry,
    a = void 0 === i || i;
  return (0, e.nikeApiInst)({
    timeoutRetry: a
  }).get("/engage/experience_nikeapp_member_view/v1?filter=upmid(".concat(t, ")")).then((function(t) {
    return t
  }))
};
var t = function(t) {
    return t && t.__esModule ? t : {
      default: t
    }
  }(require("../lib/dot-prop")),
  e = require("./common");