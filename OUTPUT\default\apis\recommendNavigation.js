Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.getFilterOptions = r;
var e = require("./common"),
  t = require("../utils/constants");

function r() {
  var r = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    s = r.filterAttributeIds,
    a = r.timeoutRetry,
    i = {
      channelId: t.CHANNEL_ID_NIKE_COM,
      language: "zh-Hans",
      marketplace: "CN",
      attributeIds: s,
      searchTerms: " ",
      seoDisabled: !0,
      status: "ACTIVE"
    };
  return (0, e.nikeApiInst)({
    timeoutRetry: a
  }).post("/recommend/navigations/v1/product_feed/threads/v2", i).then((function(e) {
    return {
      filters: e.data.filters,
      categories: e.data.categories
    }
  }))
}
exports.default = r;