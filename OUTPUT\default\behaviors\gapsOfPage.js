Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("../utils/eventEmitter"),
  t = require("../utils/pageTransition"),
  r = require("../utils/wxauth"),
  i = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../utils/configureStore")),
  o = require("../state/global-components");
exports.default = Behavior({
  methods: {
    triggerGoTo: function(r) {
      (0, e.emit)(t.PAGE_TRANSITION, r)
    },
    goToSetting: function(e) {
      var t = e.currentTarget.dataset.type;
      (0, r.openSetting)((function(e) {
        e.authSetting["scope.".concat(t)] && i.default.dispatch((0, o.hideModal)())
      }))
    }
  }
});