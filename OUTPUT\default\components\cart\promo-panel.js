var e = require("../../@babel/runtime/helpers/objectSpread2"),
  t = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  o = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../@babel/runtime/helpers/slicedToArray"),
  a = require("../../vendor/redux"),
  n = require("../../lib/mp-redux/index"),
  i = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../../utils/configureStore")),
  s = require("../../state/global-components"),
  d = require("../../state/cart/selectors"),
  l = require("../../state/app"),
  c = require("../../state/cart/actions"),
  u = require("../../utils/analytics/core"),
  h = require("../../state/selectors/promos"),
  m = require("../../state/promos"),
  p = require("../../state/coupons");
var C = "0.3s",
  P = "height",
  g = "ease-in-out",
  T = {
    options: {
      addGlobalClass: !0
    },
    properties: {
      isOpen: {
        type: Boolean
      },
      maxHeight: {
        type: Number
      },
      isGuest: {
        type: Boolean
      }
    },
    data: {
      DURATION_CART_PANEL_HEIGHT_UPDATE: C,
      CONTENT_FADING_DURATION: 300,
      PANEL_TRANSITION_PROPERTY: P,
      PANEL_TRANSITION_TIMING_FUNCTION: g,
      PANEL_TRANSITION_DURATION: C,
      pageReOpened: !1,
      changeDetector: -1,
      dimensions: {},
      contentMaxHeight: 0
    },
    lifetimes: {
      attached: function() {
        this.setData({
          DURATION_CART_PANEL_HEIGHT_UPDATE: C,
          CONTENT_FADING_DURATION: 300,
          PANEL_TRANSITION_PROPERTY: P,
          PANEL_TRANSITION_TIMING_FUNCTION: g,
          PANEL_TRANSITION_DURATION: C
        })
      }
    },
    pageLifetimes: {
      show: function() {
        var e = this;
        this.setData({
          pageReOpened: !0
        });
        var t = !this.data.selectedCartHasDiscount,
          o = setInterval((function() {
            var o = e.data.selectedCartHasDiscount;
            t !== o && (e.resizePanel(), t = o)
          }), 100);
        this.setData({
          changeDetector: o
        })
      },
      hide: function() {
        this.setData({
          pageReOpened: !1
        }), this.data.changeDetector && clearInterval(this.data.changeDetector)
      }
    },
    methods: {
      hideCartPromoInputAndResize: function() {
        (0, u.sendTrackEvent)("Cart_Add_Promo_Close"), this.hideAddPromoCodeForm(), this.resizePanel()
      },
      tryCloseCartPanel: function() {
        this.data.isAddPromoCodeFormVisible ? this.hideCartPromoInputAndResize() : (this.closeCartPanel(), (0, u.sendTrackEvent)("Cart_Close_Promocode"))
      },
      handleClosePanel: function() {
        this.data.isAddPromoCodeFormVisible && this.hideAddPromoCodeForm(), this.closeCartPanel(), (0, u.sendTrackEvent)("Cart_Close_Promocode"), this.triggerEvent("close")
      },
      handleSetHeight: function(e) {
        var t = e.detail.height;
        this.selectComponent("#cart-panel").setHeight(t)
      },
      handleAutoAdjustHeight: function() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {
            detail: {}
          },
          t = e.detail,
          o = t.preferredHeight,
          r = t.disablePanelTransitions,
          a = void 0 !== r && r,
          n = o || 0;
        o && o >= this.properties.maxHeight && (n = this.properties.maxHeight);
        var i = this.selectComponent("#cart-panel");
        i.getMaxHeight() < n && i.setMaxHeight(n), a ? this.setData({
          PANEL_TRANSITION_PROPERTY: ""
        }) : this.setData({
          PANEL_TRANSITION_PROPERTY: P
        }), i.refresh()
      },
      measureAndCalculateDimensions: function() {
        var e = this,
          t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : function() {},
          o = {
            totalHeight: 0,
            content: {
              height: 0
            },
            header: {
              height: 0
            },
            footer: {
              height: 0
            }
          };
        this.createSelectorQuery().selectAll(".dimension-query-node").boundingClientRect().exec((function(a) {
          var n = r(a, 1)[0],
            i = 0;
          n.forEach((function(e) {
            var t = e.height;
            i += t, o[e.id].height = t, o.totalHeight = i
          }));
          var s = e.properties.maxHeight - o.footer.height - o.header.height;
          e.setData({
            dimensions: o,
            contentMaxHeight: s
          }, t)
        }))
      },
      adjustHeight: function(e) {
        e ? this.triggerEvent("setHeight", {
          height: e
        }, {
          bubbles: !0
        }) : this.handleAutoAdjustHeight()
      },
      handleTryToAddPromo: function() {
        (0, u.sendTrackEvent)("Cart_Add_Promo"), this.showAddPromoCodeForm()
      },
      handleTapPromoSelection: function(e) {
        var r = this;
        return o(t().mark((function a() {
          var n;
          return t().wrap((function(a) {
            for (;;) switch (a.prev = a.next) {
              case 0:
                n = e.detail.promo, r.changePromoSelection(n.promoCode, !n.selected), r.triggerEvent("startLoadingTask", {
                  fn: function() {
                    var e = o(t().mark((function e() {
                      return t().wrap((function(e) {
                        for (;;) switch (e.prev = e.next) {
                          case 0:
                            return e.abrupt("return", r.doCartReviews());
                          case 1:
                          case "end":
                            return e.stop()
                        }
                      }), e)
                    })));
                    return function() {
                      return e.apply(this, arguments)
                    }
                  }()
                }, {
                  bubbles: !0,
                  composed: !0
                });
              case 2:
              case "end":
                return a.stop()
            }
          }), a)
        })))()
      },
      handleTapPromoTips: function(r) {
        var a = this;
        return o(t().mark((function n() {
          var i, s, d;
          return t().wrap((function(n) {
            for (;;) switch (n.prev = n.next) {
              case 0:
                i = r.detail.promo, s = i.invalid, d = i.deprecated, s || d || (a.triggerEvent("startLoadingTask", {
                  fn: function() {
                    var r = o(t().mark((function o() {
                      var r, n, s;
                      return t().wrap((function(t) {
                        for (;;) switch (t.prev = t.next) {
                          case 0:
                            return r = "优惠券不可用", n = "结算商品不满足此优惠券的使用条件", s = {}, t.next = 3, a.isPromoValid(i);
                          case 3:
                            if (!t.sent) {
                              t.next = 7;
                              break
                            }
                            r = "优惠券优惠较小", n = "此优惠券与我们推荐的优惠券不可同时使用。请使用推荐的优惠券，享受最大优惠", s = e(e({}, s), {}, {
                              deprecated: !0
                            }), t.next = 8;
                            break;
                          case 7:
                            s = e(e({}, s), {}, {
                              invalid: !0
                            });
                          case 8:
                            a.showModal({
                              title: r,
                              detail: n,
                              isShowingCloseBtn: !1,
                              isShowingCancelBtn: !1,
                              confirmHandlerName: "handleCartClosePromoTip",
                              confirmText: "我知道了"
                            }), a.updatePromoStatus(i.promoCode, s);
                          case 10:
                          case "end":
                            return t.stop()
                        }
                      }), o)
                    })));
                    return function() {
                      return r.apply(this, arguments)
                    }
                  }()
                }, {
                  bubbles: !0,
                  composed: !0
                }), (0, u.sendTrackEvent)("Cart_Promo_Invalid_Info", {
                  promoCode: i.promoCode,
                  errorCode: "not_applied"
                }));
              case 2:
              case "end":
                return n.stop()
            }
          }), n)
        })))()
      },
      resizePanel: function() {
        var e = this;
        this.measureAndCalculateDimensions((function() {
          e.adjustHeight()
        }))
      },
      handleTapCartPanelCta: function() {
        this.data.isAddPromoCodeFormVisible ? this.selectComponent("#promo-code-panel").handleApplyPromoCodeTap() : (this.triggerEvent("doTapCheckout", {}, {
          bubbles: !0,
          composed: !0
        }), (0, u.sendTrackEvent)("Cart_Promo_Confirm_Checkout"))
      }
    }
  },
  A = {
    showAddPromoCodeForm: c.showAddPromoCodeForm,
    hideAddPromoCodeForm: c.hideAddPromoCodeForm,
    changePromoSelection: m.changePromoSelection,
    updatePromoStatus: m.updatePromoStatus,
    doCartReviews: c.doCartReviews,
    isPromoValid: c.isPromoValid,
    closeCartPanel: c.closeCartPanel,
    showToast: s.showToast,
    showModal: s.showModal,
    hideModal: s.hideModal,
    clearPromoCodeInputError: c.clearPromoCodeInputError,
    savePromoCodeInput: c.savePromoCodeInput
  };
(0, a.compose)(Component, (0, n.connectComponent)(i.default)((function(e) {
  return {
    isIphoneX: (0, l.isDeviceIPhoneX)(e),
    appliedPromos: (0, h.getAppliedPromosForUI)(e),
    appliedCoupons: (0, p.getAppliedCoupons)(e),
    allPromos: (0, h.getAllPromosForUI)(e),
    selectedCartHasDiscount: (0, d.getSelectedCartHasDiscount)(e),
    cartSelectedTotal: (0, d.getFormattedSelectedTotal)(e),
    paymentAmount: (0, d.getCartPaymentAmount)(e),
    isAddPromoCodeFormVisible: (0, d.getIsAddPromoCodeFormVisible)(e),
    shouldEnableCta: (0, d.getShouldEnableCta)(e),
    subtotalFormatted: (0, d.getFormattedSelectedSubtotal)(e),
    subtotal: (0, d.getSelectedTotals)(e),
    selectedCartFormattedDiscountTotal: (0, d.getSelectedCartFormattedDiscountTotal)(e),
    promotionDiscounts: (0, h.getPromotionDiscounts)(e),
    shippingFee: (0, d.getFormattedSelectedFulfillment)(e)
  }
}), A))(T);