var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var n = c(require("../lib/mp")),
  r = require("../constants/page-route"),
  a = require("../state/user"),
  o = require("../utils/constants"),
  i = c(require("../vendor/qs")),
  s = c(require("../utils/configureStore")),
  u = require("../utils/analytics/core");

function c(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
exports.default = Behavior({
  pageLifetimes: {
    show: function() {
      this.onAuthPageReturn()
    }
  },
  data: {
    showLoginJoinPanel: !1,
    isUserInfoAuthorized: !1
  },
  methods: {
    onAuthPageReturn: function() {
      (0, a.getShouldHandleSocialLoginFailNotLinked)(s.default.getState()) && this.selectComponent("#join-login-panel") && (this.joinOrLogin(), this.triggerEvent("retryJoinOrLogin", null, {
        bubbles: !0,
        composed: !0
      }))
    },
    afterGetUserProfile: function(r) {
      var u = this;
      return t(e().mark((function c() {
        var l;
        return e().wrap((function(c) {
          for (;;) switch (c.prev = c.next) {
            case 0:
              l = r.detail, u.triggerEvent("startLoadingTask", {
                fn: function() {
                  var r = t(e().mark((function t() {
                    var r, c, f, p, d, h, g;
                    return e().wrap((function(e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          if (s.default.dispatch({
                              type: a.SOCIAL_LOGIN
                            }), s.default.dispatch((0, a.setCheckEmailDOBAfterLogin)({
                              value: !1,
                              url: ""
                            })), r = {}, e.prev = 2, l.userInfo) {
                            e.next = 6;
                            break
                          }
                          throw "user profile get failed";
                        case 6:
                          return e.next = 8, n.default.login();
                        case 8:
                          return c = e.sent, f = c.code, e.next = 12, n.default.getUserInfo();
                        case 12:
                          return p = e.sent, d = p.iv, h = p.encryptedData, u.setData({
                            isUserInfoAuthorized: !0
                          }), r.action = o.UNITE_ACTION_SOCIAL_LOGIN, r.userIV = d, r.userCode = f, r.userData = h, g = i.default.stringify(r, {
                            skipNulls: !0
                          }), e.next = 19, n.default.navigateTo({
                            url: "/pages/auth/accounts?".concat(g)
                          });
                        case 19:
                          e.next = 24;
                          break;
                        case 21:
                          e.prev = 21, e.t0 = e.catch(2), u.joinOrLogin();
                        case 24:
                        case "end":
                          return e.stop()
                      }
                    }), t, null, [
                      [2, 21]
                    ])
                  })));
                  return function() {
                    return r.apply(this, arguments)
                  }
                }()
              }, {
                bubbles: !0,
                composed: !0
              });
            case 2:
            case "end":
              return c.stop()
          }
        }), c)
      })))()
    },
    getPhoneNumber: function(r) {
      var a = this;
      return t(e().mark((function s() {
        var c, l, f, p;
        return e().wrap((function(s) {
          for (;;) switch (s.prev = s.next) {
            case 0:
              c = r.detail, l = c.errMsg, f = c.iv, p = c.encryptedData, a.triggerEvent("startLoadingTask", {
                fn: function() {
                  var r = t(e().mark((function t() {
                    var r, s;
                    return e().wrap((function(e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          if (l !== o.MSG_GET_PHONE_NUMBER_SUCCESS) {
                            e.next = 6;
                            break
                          }
                          return (0, u.sendTrackEvent)("Join_Nikeplus_Wechat_Mobile_Allow"), e.next = 4, a.doSocialOrNormalJoin({
                            phoneIV: f,
                            phoneCode: a.phoneCode,
                            phoneData: p
                          });
                        case 4:
                          e.next = 12;
                          break;
                        case 6:
                          return (0, u.sendTrackEvent)("Join_Nikeplus_Wechat_Mobile_Deny"), (r = {}).action = o.UNITE_ACTION_JOIN, s = i.default.stringify(r, {
                            skipNulls: !0
                          }), e.next = 12, n.default.navigateTo({
                            url: "/pages/auth/accounts?".concat(s)
                          });
                        case 12:
                        case "end":
                          return e.stop()
                      }
                    }), t)
                  })));
                  return function() {
                    return r.apply(this, arguments)
                  }
                }()
              }, {
                bubbles: !0,
                composed: !0
              });
            case 2:
            case "end":
              return s.stop()
          }
        }), s)
      })))()
    },
    doSocialOrNormalJoin: function(r) {
      var a = this;
      return t(e().mark((function s() {
        var u;
        return e().wrap((function(s) {
          for (;;) switch (s.prev = s.next) {
            case 0:
              (u = {}).action = o.UNITE_ACTION_SOCIAL_JOIN, r && (u.phoneIV = r.phoneIV, u.phoneCode = r.phoneCode, u.phoneData = r.phoneData), a.triggerEvent("startLoadingTask", {
                fn: function() {
                  var a = t(e().mark((function t() {
                    var a, s, c, l, f, p;
                    return e().wrap((function(e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          return e.next = 2, n.default.login();
                        case 2:
                          return a = e.sent, s = a.code, e.prev = 4, e.next = 7, n.default.getUserInfo();
                        case 7:
                          c = e.sent, l = c.iv, f = c.encryptedData, u.userIV = l, u.userCode = s, u.userData = f, e.next = 16;
                          break;
                        case 13:
                          e.prev = 13, e.t0 = e.catch(4), u.action = o.UNITE_ACTION_JOIN;
                        case 16:
                          return r || (u.action = o.UNITE_ACTION_JOIN), p = i.default.stringify(u, {
                            skipNulls: !0
                          }), e.next = 20, n.default.navigateTo({
                            url: "/pages/auth/accounts?".concat(p)
                          });
                        case 20:
                        case "end":
                          return e.stop()
                      }
                    }), t, null, [
                      [4, 13]
                    ])
                  })));
                  return function() {
                    return a.apply(this, arguments)
                  }
                }()
              }, {
                bubbles: !0,
                composed: !0
              });
            case 2:
            case "end":
              return s.stop()
          }
        }), s)
      })))()
    },
    joinOrLogin: function(a) {
      return t(e().mark((function t() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (!a) {
                e.next = 5;
                break
              }
              return e.next = 3, n.default.navigateTo({
                url: "".concat(r.ROUTE_ACCOUNTS_DEEPLINK, "?params=").concat(encodeURIComponent(JSON.stringify(a)))
              });
            case 3:
              e.next = 7;
              break;
            case 5:
              return e.next = 7, n.default.navigateTo({
                url: r.ROUTE_ACCOUNTS_DEEPLINK
              });
            case 7:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    },
    handleCloseJoinOrLoginPanel: function() {
      this.setData({
        showLoginJoinPanel: !1
      })
    }
  }
});