var t = require("../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.createOrUpdateCartByUser = function(t) {
  return c.apply(this, arguments)
}, exports.deleteCartById = function() {
  return p.apply(this, arguments)
}, exports.getCart = function(t) {
  var e = t.brand,
    r = void 0 === e ? "NIKE" : e,
    n = t.channel,
    u = void 0 === n ? "WECHAT" : n,
    a = t.country,
    o = void 0 === a ? "CN" : a,
    s = ["filter=brand(".concat(r, ")"), "filter=channel(".concat(u, ")"), "filter=country(".concat(o, ")")].join("&");
  return (0, i.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/carts/v2?".concat(s))
}, exports.getCartInfoBySkuId = function() {
  return l.apply(this, arguments)
}, exports.getCartPromotionReviewsJob = v, exports.getCartReviewsJob = d, exports.patchCart = function(t, e) {
  var r = t.accessToken,
    n = getApp().globalData.appConfig.nikeApiBaseurl,
    u = "".concat(n, "/buy/carts/v2/CN/NIKE/WECHAT"),
    a = {};
  return a.Authorization = "Bearer ".concat(r), (0, i.nikeApiInst)().patch(u, e, {
    header: a
  })
}, exports.postCartPromotionReviews = function(t) {
  return h.apply(this, arguments)
}, exports.postCartReviews = function(t) {
  return f.apply(this, arguments)
};
var r = s(require("../lib/dot-prop")),
  n = require("../utils/common"),
  u = require("../utils/constants"),
  a = require("../utils/payment-error"),
  i = require("./common"),
  o = s(require("./normalizers/carts"));

function s(t) {
  return t && t.__esModule ? t : {
    default: t
  }
}

function c() {
  return (c = e(t().mark((function e(r) {
    var n, u, a, o, s, c;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return n = r.cartId, u = r.cartItems, a = void 0 === u ? [] : u, o = r.promoCodes, s = void 0 === o ? [] : o, c = {
            id: n,
            items: a.map((function(t) {
              return {
                id: t.id,
                skuId: t.skuId,
                quantity: t.quantity
              }
            })),
            promotionCodes: s.filter((function(t) {
              return t.length > 0
            }))
          }, t.abrupt("return", (0, i.nikeApiInst)({
            timeoutRetry: !0,
            usingWxCloud: !0
          }).put("/buy/carts/v2/CN/NIKE/WECHAT", c));
        case 3:
        case "end":
          return t.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function p() {
  return (p = e(t().mark((function e() {
    var r, n, u = arguments;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          return r = u.length > 0 && void 0 !== u[0] ? u[0] : "", n = "/buy/carts/v2/".concat(r), t.abrupt("return", (0, i.nikeApiInst)({
            timeoutRetry: !0,
            usingWxCloud: !0
          }).delete(n));
        case 3:
        case "end":
          return t.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function l() {
  return (l = e(t().mark((function e() {
    var n, a, s, c, p, l, d, f, v, h = arguments;
    return t().wrap((function(t) {
      for (;;) switch (t.prev = t.next) {
        case 0:
          if (0 !== (n = h.length > 0 && void 0 !== h[0] ? h[0] : []).length) {
            t.next = 3;
            break
          }
          return t.abrupt("return", {});
        case 3:
          for (a = ["filter=productInfo.merchProduct.status(ACTIVE)", "filter=marketplace(CN)", "filter=language(zh-Hans)", "filter=channelId(".concat(u.CHANNEL_ID_PRODUCT_THREAD, ")"), "filter=productInfo.merchProduct.channels(WeChat)"].join("&"), s = "/product_feed/threads/v2?".concat(a), c = n.map((function(t) {
              return t.skuId
            })), p = [], l = Math.floor((c.length - 1) / 50) + 1, d = 0; d < l; d += 1) p.push(c.slice(50 * d, 50 * (d + 1)));
          return f = p.map((function(t) {
            var e = t.join(",");
            return t.length > 1 ? i.wechatApiInst.get("".concat(s, "&filter=skuIds(").concat(e, ")")).then((function(t) {
              return r.default.get(t, "data.objects")
            })) : (0, i.nikeApiInst)().get("/product_feed/threads/v3?".concat(a, "&filter=skuIds(").concat(e, ")")).then((function(t) {
              return r.default.get(t, "data.objects")
            }))
          })), t.next = 8, Promise.all(f);
        case 8:
          return v = t.sent.flat(), t.abrupt("return", o.default.normalizeGetCartSKUs({
            skuList: c,
            productFeeds: v
          }));
        case 10:
        case "end":
          return t.stop()
      }
    }), e)
  })))).apply(this, arguments)
}

function d(t) {
  return (0, i.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/cart_reviews/v2/".concat(t))
}

function f() {
  return (f = e(t().mark((function r(u) {
    var s, c, p, l, f, v, h, m, y, C, g, I;
    return t().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return s = u.country, c = void 0 === s ? "CN" : s, p = u.items, l = void 0 === p ? [] : p, f = u.promoCodes, v = void 0 === f ? [] : f, h = (0, n.uuid)(), m = function() {
            var r = e(t().mark((function e() {
              var r;
              return t().wrap((function(t) {
                for (;;) switch (t.prev = t.next) {
                  case 0:
                    return r = "/buy/cart_reviews/v2/".concat(h), t.next = 3, (0, i.nikeApiInst)({
                      timeoutRetry: !0,
                      usingWxCloud: !0
                    }).put(r, {
                      request: {
                        country: c,
                        currency: "CNY",
                        locale: "zh_CN",
                        items: l.map((function(t) {
                          return {
                            id: t.id,
                            skuId: t.skuId,
                            quantity: t.quantity,
                            valueAddedServices: [],
                            fulfillmentDetails: {
                              type: "SHIP",
                              location: {
                                type: "address/shipping",
                                postalAddress: t.shippingAddress
                              }
                            }
                          }
                        })),
                        promotionCodes: v.filter((function(t) {
                          return t.length > 0
                        }))
                      }
                    });
                  case 3:
                    return t.abrupt("return", t.sent);
                  case 4:
                  case "end":
                    return t.stop()
                }
              }), e)
            })));
            return function() {
              return r.apply(this, arguments)
            }
          }(), y = function() {
            return d(h)
          }, C = function(t) {
            var e, r, n = t.result;
            return n && null !== (e = n.data) && void 0 !== e && e.error ? n.data.error : n && (n.errors || n.warnings) ? n.errors || n.warnings : 401 === (null === (r = t.header) || void 0 === r ? void 0 : r.status) && a.FulfillmentOfferingsUnauthorized
          }, r.prev = 2, r.next = 5, (0, i.asyncApiCallExecutor)({
            initialRequest: m,
            poll: y,
            failed: C,
            getEta: function(t) {
              return t && t.data && t.data.eta ? t.data.eta : 0
            }
          });
        case 5:
          return g = r.sent, I = g.result.data, r.abrupt("return", o.default.normalizeCartReviewResponse({
            data: I.response
          }));
        case 10:
          return r.prev = 10, r.t0 = r.catch(2), r.abrupt("return", Promise.reject(r.t0));
        case 13:
        case "end":
          return r.stop()
      }
    }), r, null, [
      [2, 10]
    ])
  })))).apply(this, arguments)
}

function v(t) {
  return (0, i.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/cart_promotion_reviews/v1/".concat(t))
}

function h() {
  return (h = e(t().mark((function r(u) {
    var a, s, c, p, l, d, f, h;
    return t().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return a = u.items, s = void 0 === a ? [] : a, c = (0, n.uuid)(), p = function() {
            var r = e(t().mark((function e() {
              var r;
              return t().wrap((function(t) {
                for (;;) switch (t.prev = t.next) {
                  case 0:
                    return r = "/buy/cart_promotion_reviews/v1/".concat(c), t.next = 3, (0, i.nikeApiInst)({
                      timeoutRetry: !0,
                      usingWxCloud: !0
                    }).put(r, {
                      request: {
                        country: "CN",
                        currency: "CNY",
                        locale: "zh_CN",
                        items: s.map((function(t) {
                          return {
                            id: t.id,
                            skuId: t.skuId,
                            quantity: t.quantity,
                            valueAddedServices: [],
                            fulfillmentDetails: {
                              type: "SHIP",
                              location: {
                                type: "address/shipping",
                                postalAddress: {
                                  country: "CN"
                                }
                              }
                            }
                          }
                        }))
                      }
                    });
                  case 3:
                    return t.abrupt("return", t.sent);
                  case 4:
                  case "end":
                    return t.stop()
                }
              }), e)
            })));
            return function() {
              return r.apply(this, arguments)
            }
          }(), l = function() {
            return v(c)
          }, d = function() {
            return !1
          }, r.prev = 2, r.next = 5, (0, i.asyncApiCallExecutor)({
            initialRequest: p,
            poll: l,
            failed: d,
            getEta: function(t) {
              return t && t.data && t.data.eta ? t.data.eta : 0
            }
          });
        case 5:
          return f = r.sent, h = f.result.data, r.abrupt("return", o.default.normalizeCartPromotionReviewResponse({
            data: h.response
          }));
        case 10:
          return r.prev = 10, r.t0 = r.catch(2), r.abrupt("return", Promise.reject(r.t0));
        case 13:
        case "end":
          return r.stop()
      }
    }), r, null, [
      [2, 10]
    ])
  })))).apply(this, arguments)
}