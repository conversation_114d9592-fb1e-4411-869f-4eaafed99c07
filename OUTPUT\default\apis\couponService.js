var r = require("../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.ERROR_CODE = void 0, exports.claimCoupon = function(r) {
  return n.apply(this, arguments)
}, exports.getStockInfo = function(r) {
  return c.apply(this, arguments)
}, exports.getUserCoupons = function() {
  return u.apply(this, arguments)
}, exports.getV2Sign = function() {
  return p.apply(this, arguments)
};
var t = require("./common");
exports.ERROR_CODE = {
  ACCOUNT_NOT_VERIFIED: "ACCOUNT_NOT_VERIFIED",
  FREQUENCY_LIMIT_EXCEED: "FREQUENCY_LIMIT_EXCEED",
  INVALID_STOCK_ID: "INVALID_STOCK_ID",
  MCH_NOT_EXISTS: "MCH_NOT_EXISTS",
  NOT_ENOUGH: "NOT_ENOUGH",
  REQUEST_BLOCKED: "REQUEST_BLOCKED",
  RULE_LIMIT: "RULE_LIMIT",
  STOCK_NOT_FOUND: "STOCK_NOT_FOUND",
  USER_ACCOUNT_ABNORMAL: "USER_ACCOUNT_ABNORMAL",
  WECHAT_USER_ACCOUNT_ABNORMAL: "WECHAT_USER_ACCOUNT_ABNORMAL"
};

function n() {
  return (n = e(r().mark((function e(n) {
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return r.prev = 0, r.abrupt("return", t.wechatApiInst.post("wechat_pay/user_coupons/v1", {
            stockId: n
          }));
        case 4:
          return r.prev = 4, r.t0 = r.catch(0), r.abrupt("return", Promise.reject(r.t0));
        case 7:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function u() {
  return (u = e(r().mark((function e() {
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return r.prev = 0, r.abrupt("return", t.wechatApiInst.get("wechat_pay/user_coupons/v2"));
        case 4:
          return r.prev = 4, r.t0 = r.catch(0), r.abrupt("return", Promise.reject(r.t0));
        case 7:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function c() {
  return (c = e(r().mark((function e(n) {
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return r.prev = 0, r.abrupt("return", t.wechatApiInst.get("wechat_pay/coupons/v1?".concat(n)));
        case 4:
          return r.prev = 4, r.t0 = r.catch(0), r.abrupt("return", Promise.reject(r.t0));
        case 7:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function p() {
  return (p = e(r().mark((function e() {
    var n, u = arguments;
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return n = u.length > 0 && void 0 !== u[0] ? u[0] : [], r.prev = 1, r.abrupt("return", t.wechatApiInst.post("wechat_pay/send_coupon_sign/v1", {
            stockIds: n
          }));
        case 5:
          return r.prev = 5, r.t0 = r.catch(1), r.abrupt("return", Promise.reject(r.t0));
        case 8:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [1, 5]
    ])
  })))).apply(this, arguments)
}