var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../utils/dataloader"),
  n = require("../../utils/data-format");
Component({
  options: {
    addGlobalClass: !0
  },
  properties: {
    eventId: {
      type: String
    },
    coach: {
      type: String
    },
    phone: {
      type: String
    }
  },
  data: {
    eventDetails: {},
    eventStartDate: "",
    eventStartTime: "",
    eventEndDate: "",
    eventEndTime: ""
  },
  lifetimes: {
    ready: function() {
      var r = this;
      return t(e().mark((function t() {
        var o, i, s, d, m, u, c;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.next = 2, a.eventLoader.load(r.data.eventId);
            case 2:
              o = e.sent, i = new Date("".concat(o.startDateUTC, "Z")), s = new Date("".concat(o.endDateUTC, "Z")), d = (0, n.formatDate)(i, !0), m = (0, n.formatTime)(i), u = (0, n.formatDate)(s, !0), c = (0, n.formatTime)(s), r.setData({
                eventDetails: o,
                eventStartDate: d,
                eventStartTime: m,
                eventEndDate: u,
                eventEndTime: c
              });
            case 10:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    }
  },
  methods: {
    onTapPhone: function() {
      wx.makePhoneCall({
        phoneNumber: this.data.phone
      })
    }
  }
});