var t = require("../../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.confettiCannon = p, exports.default = function() {
  return g().apply(this, arguments)
}, exports.reset = void 0;
var e = 750 / wx.getSystemInfoSync().windowWidth * 80;

function a() {}

function o(t) {
  var e = module.exports.Promise,
    o = void 0 !== e ? e : global.Promise;
  return "function" == typeof o ? new o(t) : (t(a, a), null)
}
var r, n = function() {
    var t, e, a = Math.floor(1e3 / 60),
      o = {},
      r = 0;
    return "function" == typeof requestAnimationFrame && "function" == typeof cancelAnimationFrame ? (t = function(t) {
      var e = Math.random();
      return o[e] = requestAnimationFrame((function n(i) {
        r === i || r + a - 1 < i ? (r = i, delete o[e], t()) : o[e] = requestAnimationFrame(n)
      })), e
    }, e = function(t) {
      o[t] && cancelAnimationFrame(o[t])
    }) : (t = function(t) {
      return setTimeout(t, a)
    }, e = function(t) {
      return clearTimeout(t)
    }), {
      frame: t,
      cancel: e
    }
  }(),
  i = {
    particleCount: 50,
    angle: 90,
    spread: 45,
    startVelocity: 45,
    decay: .9,
    gravity: 1,
    drift: 0,
    ticks: 200,
    x: .5,
    y: .5,
    shapes: ["square", "circle", "rectangle"],
    zIndex: 100,
    colors: ["#26ccff", "#a25afd", "#ff5e7e", "#88ff5a", "#fcff42", "#ffa62d", "#ff36ff"],
    disableForReducedMotion: !1,
    scalar: 1
  };

function l(t, e, a) {
  return function(t, e) {
    return e ? e(t) : t
  }(t && function(t) {
    return !(null == t)
  }(t[e]) ? t[e] : i[e], a)
}

function c(t) {
  return t < 0 ? 0 : Math.floor(t)
}

function s(t, e) {
  return Math.floor(Math.random() * (e - t)) + t
}

function h(t) {
  return parseInt(t, 16)
}

function u(t) {
  return t.map(d)
}

function d(t) {
  var e = String(t).replace(/[^0-9a-f]/gi, "");
  return e.length < 6 && (e = e[0] + e[0] + e[1] + e[1] + e[2] + e[2]), {
    r: h(e.substring(0, 2)),
    g: h(e.substring(2, 4)),
    b: h(e.substring(4, 6))
  }
}

function f(t) {
  t.width = t._width, t.height = t._height
}

function b(t) {
  var e = t.angle * (Math.PI / 180),
    a = t.spread * (Math.PI / 180);
  return {
    x: t.x,
    y: t.y,
    wobble: 8 * Math.random(),
    wobbleSpeed: Math.min(.05, .05 * Math.random() + .02),
    velocity: .7 * t.startVelocity + Math.random() * t.startVelocity,
    angle2D: -e + (.5 * a - Math.random() * a),
    tiltAngle: (.4 * Math.random() + .1) * Math.PI,
    color: t.color,
    shape: t.shape,
    tick: 0,
    totalTicks: t.ticks,
    decay: t.decay,
    drift: t.drift,
    random: Math.random() + 2,
    tiltSin: 0,
    tiltCos: 0,
    wobbleX: 0,
    wobbleY: 0,
    gravity: 3 * t.gravity,
    ovalScalar: .6,
    scalar: Math.random() > .5 ? .8 * Math.random() + t.scalar : t.scalar - .6 * Math.random(),
    flat: t.flat,
    isAlpha: t.isAlpha,
    stripRotation: (Math.random() - .5) * Math.PI,
    stripRotationSpeed: .05 * (Math.random() - .5),
    stripRotationAcceleration: 5e-4 * (Math.random() - .5),
    bend: 30 * Math.random()
  }
}

function m(a, o, r) {
  o.tick++, o.isAlpha && (o.tick, o.totalTicks), o.x += Math.cos(o.angle2D) * o.velocity + o.drift * Math.random(), o.y += Math.sin(o.angle2D) * o.velocity + o.gravity, o.velocity *= o.decay, o.flat ? (o.wobble = 0, o.wobbleX = o.x + 10 * o.scalar, o.wobbleY = o.y + 10 * o.scalar, o.tiltSin = 0, o.tiltCos = 0, o.random = 1) : (o.wobble += o.wobbleSpeed, o.wobbleX = o.x + 10 * o.scalar * Math.cos(o.wobble), o.wobbleY = o.y + 10 * o.scalar * Math.sin(o.wobble), o.tiltAngle += .1, o.tiltSin = Math.sin(o.tiltAngle), o.tiltCos = Math.cos(o.tiltAngle), o.random = Math.random() + 2), "rectangle" === o.shape && (o.stripRotationSpeed += o.stripRotationAcceleration, o.stripRotation += o.stripRotationSpeed, o.stripRotation > 2 * Math.PI && (o.stripRotation -= 2 * Math.PI), o.bend += .5, o.bend > 30 && (o.bend = 0));
  var n = o.x + o.random * o.tiltCos,
    i = o.y + o.random * o.tiltSin,
    l = o.wobbleX + o.random * o.tiltCos,
    c = o.wobbleY + o.random * o.tiltSin;
  if (o.y > r.height - e) {
    var s = 1 - ((o.y - (r.height - e)) / e + .7);
    a.fillStyle = "rgba(".concat(o.color.r, ", ").concat(o.color.g, ", ").concat(o.color.b, ", ").concat(s, ")")
  } else a.fillStyle = "rgba(".concat(o.color.r, ", ").concat(o.color.g, ", ").concat(o.color.b, ", 1)");
  if (a.beginPath(), "rectangle" === o.shape) {
    var h = Math.PI / 10 * o.wobble,
      u = Math.cos(h),
      d = Math.sin(h),
      f = (n + l) / 2,
      b = (i + c) / 2,
      m = (n - f) * u - (i - b) * d + f,
      M = (n - f) * d + (i - b) * u + b,
      p = (l - f) * u - (c - b) * d + f,
      g = (l - f) * d + (c - b) * u + b,
      y = Math.atan2(g - M, p - m),
      v = 2 * Math.sqrt(Math.pow(p - m, 2) + Math.pow(g - M, 2)),
      w = 3 * Math.abs(Math.sin(y)),
      x = v * o.scalar < 50 ? v * o.scalar : 50,
      S = w * o.scalar,
      A = o.bend * Math.PI / 180,
      C = Math.cos(A),
      T = p - S / 2 - S / 2 * Math.sin(A),
      P = g - x / 2 - x / 2 * (1 - C);
    a.save(), a.translate(T + S / 2, P + x / 2), a.rotate(o.stripRotation),
      function(e, a, o, r, n, i) {
        i = "number" == typeof i ? {
          tl: i,
          tr: i,
          br: i,
          bl: i
        } : t({
          tl: 0,
          tr: 0,
          br: 0,
          bl: 0
        }, i), e.moveTo(a + i.tl, o), e.lineTo(a + r - i.tr, o), e.quadraticCurveTo(a + r, o, a + r, o + i.tr), e.lineTo(a + r, o + n - i.br), e.quadraticCurveTo(a + r, o + n, a + r - i.br, o + n), e.lineTo(a + i.bl, o + n), e.quadraticCurveTo(a, o + n, a, o + n - i.bl), e.lineTo(a, o + i.tl), e.quadraticCurveTo(a, o, a + i.tl, o)
      }(a, -S / 2, -x / 2, S, x, 1.5), a.restore()
  } else "circle" === o.shape ? a.ellipse ? a.ellipse(o.x, o.y, Math.abs(l - n) * o.ovalScalar, Math.abs(c - i) * o.ovalScalar, Math.PI / 10 * o.wobble, 0, 2 * Math.PI) : function(t, e, a, o, r, n, i, l, c) {
    t.save(), t.translate(e, a), t.rotate(n), t.scale(o, r), t.arc(0, 0, 1, i, l, c), t.restore()
  }(a, o.x, o.y, Math.abs(l - n) * o.ovalScalar, Math.abs(c - i) * o.ovalScalar, Math.PI / 10 * o.wobble, 0, 2 * Math.PI) : (a.moveTo(Math.floor(o.x), Math.floor(o.y)), a.lineTo(Math.floor(o.wobbleX), Math.floor(i)), a.lineTo(Math.floor(l), Math.floor(c)), a.lineTo(Math.floor(n), Math.floor(o.wobbleY)));
  return a.closePath(), a.fill(), o.tick < o.totalTicks && o.y <= r.height
}

function M(t, e, a, r, i) {
  var l, c, s = e.slice(),
    h = t.getContext("2d"),
    u = o((function(e) {
      function o() {
        l = c = null, h.clearRect(0, 0, r.width, r.height), i(), e()
      }
      l = n.frame((function e() {
        r.width || r.height || (a(t), r.width = t.width, r.height = t.height), h.clearRect(0, 0, r.width, r.height), (s = s.filter((function(e) {
          return m(h, e, t)
        }))).length ? l = n.frame(e) : o()
      })), c = o
    }));
  return {
    addFettis: function(t) {
      return s = s.concat(t), u
    },
    canvas: t,
    promise: u,
    reset: function() {
      l && n.cancel(l), c && c()
    }
  }
}

function p(t, e) {
  var a, r = !t,
    n = !!l(e || {}, "resize"),
    h = !1,
    d = l(e, "disableForReducedMotion", Boolean),
    m = null,
    p = f,
    g = !(!t || !m || !t.__confetti_initialized),
    y = "function" == typeof matchMedia && matchMedia("(prefers-reduced-motion)").matches;

  function v(e) {
    var f = d || l(e, "disableForReducedMotion", Boolean),
      v = l(e, "zIndex", Number);
    if (f && y) return o((function(t) {
      t()
    }));
    r && a ? t = a.canvas : r && !t && (t = getCanvas(v), document.body.appendChild(t)), n && !g && p(t);
    var w = {
      width: t.width,
      height: t.height
    };

    function x() {
      if (m) {
        var e = {
          getBoundingClientRect: function() {
            if (!r) return t.getBoundingClientRect()
          }
        };
        return p(e), void m.postMessage({
          resize: {
            width: e.width,
            height: e.height
          }
        })
      }
      w.width = w.height = null
    }

    function S() {
      a = null, n && (h = !1, global.removeEventListener("resize", x)), r && t && (document.body.contains(t) && document.body.removeChild(t), t = null, g = !1)
    }
    return m && !g && m.init(t), g = !0, m && (t.__confetti_initialized = !0), n && !h && (h = !0), m ? m.fire(e, w, S) : function(e, o, r) {
      for (var n = l(e, "particleCount", c), h = l(e, "angle", Number), d = l(e, "spread", Number), f = l(e, "startVelocity", Number), m = l(e, "decay", Number), g = l(e, "gravity", Number), y = l(e, "drift", Number), v = l(e, "colors", u), w = l(e, "ticks", Number), x = l(e, "shapes"), S = l(e, "scalar"), A = !!l(e, "flat"), C = function(t) {
          var e = l(t, "origin", Object);
          return e.x = l(e, "x", Number), e.y = l(e, "y", Number), e
        }(e), T = n, P = [], R = t.width * C.x, I = t.height * C.y; T--;) P.push(b({
        x: R,
        y: I,
        angle: h,
        spread: d,
        startVelocity: f,
        color: v[T % v.length],
        shape: x[s(0, x.length)],
        ticks: w,
        decay: m,
        gravity: g,
        drift: y,
        scalar: S,
        flat: A,
        isAlpha: "boolean" == typeof e.isAlpha ? e.isAlpha : i.isAlpha
      }));
      return a ? a.addFettis(P) : (a = M(t, P, p, o, r)).promise
    }(e, w, S)
  }
  return v.reset = function() {
    m && m.reset(), a && a.reset()
  }, v
}

function g() {
  return r || (r = p(null, {
    useWorker: !1,
    resize: !1
  })), r
}
exports.reset = function() {
  g().reset()
};