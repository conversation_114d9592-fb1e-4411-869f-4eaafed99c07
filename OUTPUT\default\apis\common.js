var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/objectSpread2"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.asyncApiCallExecutor = function(e) {
  return H.apply(this, arguments)
}, exports.callAsyncJob = function(e, r) {
  return new Promise((function(n, a) {
    ! function o() {
      w().request(t(t({}, r), {}, {
        method: "GET",
        url: e
      })).then((function(e) {
        var t = u.default.get(e, "data", {});
        "COMPLETED" !== t.status ? t.eta ? setTimeout(o, t.eta) : a(e) : n(e)
      }), (function(e) {
        return a(e)
      })).catch((function(e) {
        return a(e)
      }))
    }()
  }))
}, exports.redeemApiInst = exports.nikeIdnAccountInst = exports.nikeApiInst = exports.marTechApiInst = exports.getGCAuthToken = exports.edaApiInst = void 0, exports.requestToRefreshToken = function(e) {
  var t = e.refreshToken,
    r = e.clientId;
  return w({
    timeoutRetry: !0
  }).request({
    url: "".concat(s.NIKE_API_BASE_URL, "/oauth/2.0/token"),
    method: "POST",
    withoutToken: !0,
    data: {
      refresh_token: t,
      client_id: r,
      grant_type: "refresh_token"
    }
  }).then((function(e) {
    return e.data
  }))
}, exports.wechatApiInst = void 0;
var n = require("../config"),
  a = h(require("../utils/user")),
  u = h(require("../lib/dot-prop")),
  o = require("../lib/httpClient/index"),
  i = require("../utils/cache"),
  s = require("./constants"),
  c = require("../utils/common"),
  d = require("../utils/constants"),
  p = h(require("../lib/httpClient/addTimeTrack")),
  l = require("../utils/dataloader");

function h(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var A = function() {
    var n = r(e().mark((function r(n) {
      var a, o, s, d, p, h, A;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (a = getApp().globalData.appConfig.accounts, (o = {}).appid = (void 0 === a ? {} : a).uxId, !n.withoutToken) {
              e.next = 3;
              break
            }
            return e.abrupt("return", n);
          case 3:
            return e.prev = 3, e.next = 6, l.authLoader.load(l.LOADER_KEY);
          case 6:
            if (s = e.sent, d = u.default.get(s, "accessToken"), p = u.default.get(s, "upmId"), d) {
              e.next = 11;
              break
            }
            throw "auth as a visitor";
          case 11:
            o.Authorization = "Bearer ".concat(d), o.upmid = p, e.next = 18;
            break;
          case 14:
            e.prev = 14, e.t0 = e.catch(3), h = (0, i.loadCache)("visitId").id, (A = (0, i.loadCache)("visitorId").id) || (h = 1, A = (0, c.uuid)(), (0, i.saveCache)("visitorId", A), (0, i.saveCache)("visitId", h)), o["x-nike-visitorid"] = A, o["x-nike-visitid"] = 1;
          case 18:
            return e.abrupt("return", t(t({}, n), {}, {
              header: t(t({}, n.header), o)
            }));
          case 19:
          case "end":
            return e.stop()
        }
      }), r, null, [
        [3, 14]
      ])
    })));
    return function(e) {
      return n.apply(this, arguments)
    }
  }(),
  E = function() {
    var n = r(e().mark((function r(n) {
      var a, o, i;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return a = {}, e.prev = 1, e.next = 4, l.gcServeAuthLoader.load(l.LOADER_KEY);
          case 4:
            o = e.sent, (i = u.default.get(o, "accessToken")) && (a["wechat-mp-token"] = "Bearer ".concat(i)), e.next = 12;
            break;
          case 9:
            return e.prev = 9, e.t0 = e.catch(1), e.abrupt("return", Promise.reject(e.t0));
          case 12:
            return e.abrupt("return", t(t({}, n), {}, {
              header: t(t({}, n.header), a)
            }));
          case 13:
          case "end":
            return e.stop()
        }
      }), r, null, [
        [1, 9]
      ])
    })));
    return function(e) {
      return n.apply(this, arguments)
    }
  }(),
  f = new o.HttpClient({
    baseURL: s.NIKE_API_BASE_URL,
    header: t(t({}, s.COMMON_HEADER), s.HEADER_CALLER_ID)
  });
(0, p.default)(f), (0, o.addErrorReport)(f), (0, o.addRetry)(f), f.subscribeReqInterceptor(A);
var _ = new o.HttpClient({
    baseURL: s.NIKE_API_BASE_URL,
    header: t(t({}, s.COMMON_HEADER), s.HEADER_CALLER_ID)
  }),
  v = new o.HttpClient({
    baseURL: s.NIKE_API_BASE_URL,
    header: t({}, s.COMMON_HEADER)
  });
_.subscribeReqInterceptor(A), (0, p.default)(_), (0, o.addErrorReport)(_), (0, o.addRetry)(_, {
  shouldRetry: function(e) {
    var t = e.errMsg || "",
      r = e.statusCode || 0;
    return !(0, c.isRequestTimeoutError)(e) && (t.indexOf("request:fail") >= 0 || r >= 500)
  }
}), (0, o.addErrorReport)(v);
var R = "".concat(s.WECHAT_CN_API_BASE_URL, "/proxy-v2");
n.CLOUD && (R = s.NIKE_API_BASE_URL);
var b = new o.HttpClient({
    baseURL: R,
    header: t(t({}, s.COMMON_HEADER), s.HEADER_CALLER_ID),
    cloud: Boolean(n.CLOUD)
  }),
  x = "nxp-api.nike.com.cn",
  I = function() {
    var n = r(e().mark((function r(n) {
      var a, o, s, p, h, A, E, f, _, v, R, b, I, C;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (!n.withoutToken) {
              e.next = 2;
              break
            }
            return e.abrupt("return", n);
          case 2:
            return a = getApp().globalData.appConfig, o = a.maiAccountId, s = a.nxpCallerId, p = a.nxpMpMediaId, h = a.appId, A = a.athleteAppId, E = {
              "Tenant-Id": o,
              "Nxp-Caller-Id": s
            }, A === h && (E = t(t({}, E), p ? {
              "Nxp-Mp-Media-Id": p
            } : {})), e.prev = 5, E.HOST = x, e.next = 9, l.authLoader.load(l.LOADER_KEY);
          case 9:
            _ = e.sent, (v = u.default.get(_, "accessToken")) && (R = n.url, l.CXP_UPM_Id_APIS.some((function(e) {
              return R.indexOf(e) > -1
            })) && (E.Authorization = "Bearer ".concat(v))), (b = null === (f = (0, i.loadCache)(d.GLOBAL_WECHAT_PHONE_NUMBER)) || void 0 === f ? void 0 : f.data) && (E["Guest-Number"] = (0, c.strToBase64)(b)), e.next = 20;
            break;
          case 16:
            e.prev = 16, e.t0 = e.catch(5), (C = null === (I = (0, i.loadCache)(d.GLOBAL_WECHAT_PHONE_NUMBER)) || void 0 === I ? void 0 : I.data) && (E["Guest-Number"] = (0, c.strToBase64)(C));
          case 20:
            return e.abrupt("return", t(t({}, n), {}, {
              header: t(t({}, n.header), E)
            }));
          case 21:
          case "end":
            return e.stop()
        }
      }), r, null, [
        [5, 16]
      ])
    })));
    return function(e) {
      return n.apply(this, arguments)
    }
  }(),
  C = new o.HttpClient({
    baseURL: x,
    header: t(t({}, s.COMMON_HEADER), s.HEADER_CALLER_ID),
    cloud: !0
  });
(0, o.addRetry)(C), C.subscribeReqInterceptor(I), (0, p.default)(b), (0, o.addErrorReport)(b), (0, o.addRetry)(b), b.subscribeReqInterceptor(A), n.CLOUD || b.subscribeReqInterceptor(E);
var T = (0, c.getWechatApiInstAppId)(),
  L = exports.wechatApiInst = new o.HttpClient({
    baseURL: "production" === (0, c.getBuildType)() ? s.WECHAT_CN_API_BASE_URL : s.WECHAT_CN_API_TEST_URL,
    header: t(t({}, s.COMMON_HEADER), {}, {
      "App-Id": T
    })
  }),
  k = function() {
    var t = r(e().mark((function t() {
      var r;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.next = 2, l.gcServeAuthLoader.load(l.LOADER_KEY);
          case 2:
            if (r = e.sent, e.t0 = (0, a.default)({
                cachedAt: r.cachedAt,
                data: r
              }), !e.t0) {
              e.next = 9;
              break
            }
            return l.gcServeAuthLoader.clear(l.LOADER_KEY), e.next = 8, l.gcServeAuthLoader.load(l.LOADER_KEY);
          case 8:
            r = e.sent;
          case 9:
            return e.abrupt("return", u.default.get(r, "accessToken"));
          case 10:
          case "end":
            return e.stop()
        }
      }), t)
    })));
    return function() {
      return t.apply(this, arguments)
    }
  }();
exports.getGCAuthToken = k;
var m = function() {
  var n = r(e().mark((function r(n) {
    var a, u;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          if (a = {}, !n.withoutToken) {
            e.next = 3;
            break
          }
          return e.abrupt("return", n);
        case 3:
          return e.prev = 3, e.next = 6, k();
        case 6:
          if (u = e.sent) {
            e.next = 9;
            break
          }
          throw "auth as a visitor";
        case 9:
          a.Authorization = "Bearer ".concat(u), e.next = 15;
          break;
        case 12:
          return e.prev = 12, e.t0 = e.catch(3), e.abrupt("return", Promise.reject(e.t0));
        case 15:
          return e.abrupt("return", t(t({}, n), {}, {
            header: t(t({}, n.header), a)
          }));
        case 16:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [3, 12]
    ])
  })));
  return function(e) {
    return n.apply(this, arguments)
  }
}();
(0, p.default)(L), (0, o.addErrorReport)(L), (0, o.addRetry)(L, {
  shouldRetry: function(e) {
    var t = e.errMsg || "",
      r = e.statusCode || 0;
    return !(0, c.isRequestTimeoutError)(e) && (t.indexOf("request:fail") >= 0 || r >= 500)
  }
}), L.subscribeReqInterceptor(m);
var w = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    t = e.timeoutRetry,
    r = void 0 !== t && t,
    n = e.usingWxCloud,
    a = void 0 !== n && n,
    u = e.isCxpCloudRequest,
    o = void 0 !== u && u;
  return o ? C : a ? b : r ? f : _
};
exports.nikeApiInst = w;
exports.nikeIdnAccountInst = function() {
  return v
};
var O = function(e) {
    return u.default.get(e, "data") || u.default.get(e, "result.data") || {}
  },
  P = function(e) {
    var t = O(e);
    return "PENDING" === t.status || "IN_PROGRESS" === t.status
  },
  D = function(e) {
    var t = O(e);
    return !!t && "COMPLETED" === t.status
  };

function H() {
  return (H = r(e().mark((function t(r) {
    var n, a, u, o, i, s, d, p, l, h, A, E, f, _;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return n = r.initialRequest, a = r.poll, u = r.completed, o = void 0 === u ? D : u, i = r.failed, s = r.shouldWait, d = void 0 === s ? P : s, p = r.getEta, l = void 0 === p ? function(e) {
            return e && e.eta ? e.eta : 0
          } : p, h = r.timeoutMilliseconds, A = void 0 === h ? 15e3 : h, e.prev = 1, e.next = 4, n();
        case 4:
          if (E = e.sent, !i(E)) {
            e.next = 7;
            break
          }
          return e.abrupt("return", Promise.reject(E));
        case 7:
          if (e.t0 = d(E), !e.t0) {
            e.next = 11;
            break
          }
          return e.next = 11, (0, c.sleep)(l(E));
        case 11:
          if (e.t1 = d(E), !e.t1) {
            e.next = 16;
            break
          }
          return e.next = 15, (0, c.waitUntil)({
            run: function() {
              return a(E)
            },
            getEta: l,
            isCompleted: o,
            totalTimeToWait: A
          });
        case 15:
          f = e.sent;
        case 16:
          if (!o(f)) {
            e.next = 19;
            break
          }
          return _ = i(f), e.abrupt("return", _ ? Promise.reject(_) : Promise.resolve(f));
        case 19:
          return e.abrupt("return", Promise.reject(new Error("async api call failed abnormally")));
        case 22:
          return e.prev = 22, e.t2 = e.catch(1), e.abrupt("return", Promise.reject(e.t2));
        case 25:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [1, 22]
    ])
  })))).apply(this, arguments)
}
exports.marTechApiInst = new o.HttpClient({
  baseURL: "production" === (0, c.getBuildType)() ? s.MAR_TECH_API_BASE_URL : s.MAR_TECH_API_TEST_URL,
  header: {
    authorizationToken: "allow",
    Auth: "allow",
    "Content-Type": "application/json",
    Authorization: "allow"
  }
}), exports.edaApiInst = new o.HttpClient({
  baseURL: "production" === (0, c.getBuildType)() ? s.EDA_API_BASE_URL : s.EDA_API_TEST_URL,
  header: {
    authorizationToken: "allow",
    Auth: "allow",
    "Content-Type": "application/json",
    Authorization: "allow"
  }
});
var g = function() {
    var n = r(e().mark((function r(n) {
      var u, o, i, d;
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            if (u = {}, !n.withoutToken) {
              e.next = 3;
              break
            }
            return e.abrupt("return", n);
          case 3:
            return e.prev = 3, e.next = 6, l.authLoader.load(l.LOADER_KEY);
          case 6:
            if (i = e.sent, e.t0 = (0, a.default)({
                cachedAt: i.cachedAt,
                data: i
              }), !e.t0) {
              e.next = 13;
              break
            }
            return l.authLoader.clear(l.LOADER_KEY), e.next = 12, l.authLoader.load(l.LOADER_KEY);
          case 12:
            i = e.sent;
          case 13:
            if (d = null === (o = i) || void 0 === o ? void 0 : o.accessToken) {
              e.next = 16;
              break
            }
            throw "auth as a visitor";
          case 16:
            u.Authorization = "Bearer ".concat(d), e.next = 21;
            break;
          case 19:
            e.prev = 19, e.t1 = e.catch(3);
          case 21:
            return e.abrupt("return", t(t({}, n), {}, {
              header: t(t(t({}, n.header), u), {}, {
                HOST: "production" === (0, c.getBuildType)() ? s.WECHAT_CN_API_BASE_HOST : s.WECHAT_CN_API_TEST_HOST
              })
            }));
          case 22:
          case "end":
            return e.stop()
        }
      }), r, null, [
        [3, 19]
      ])
    })));
    return function(e) {
      return n.apply(this, arguments)
    }
  }(),
  y = exports.redeemApiInst = new o.HttpClient({
    header: t(t({}, s.COMMON_HEADER), {}, {
      "App-Id": "wechat:mp:".concat((0, c.isPlusBeta)() ? n.PLUS_BETA : n.NIKE_PLUS)
    }),
    cloud: Boolean(n.CLOUD)
  });
(0, o.addRetry)(y), y.subscribeReqInterceptor(g);