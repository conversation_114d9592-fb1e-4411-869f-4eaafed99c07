var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var t, n = require("../apis/privacy"),
  a = require("../utils/analytics/core"),
  u = require("../utils/cache");
exports.default = Behavior({
  methods: {
    checkPrivacy: function() {
      return r(e().mark((function r() {
        var t, a, s;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return t = getApp(), a = t.globalData, s = a.privacyPromise || (0, n.fetchAllowedPrivacy)().then((function(e) {
                var r = e.userAgreement,
                  t = e.userAgreementBrief,
                  n = e.userAgreementVersion;
                return (0, u.saveCache)("userAgreementBrief", "", t), (0, u.saveCache)("userAgreementVersion", "", n), r
              })), e.next = 3, s;
            case 3:
              return e.abrupt("return", e.sent);
            case 4:
            case "end":
              return e.stop()
          }
        }), r)
      })))()
    },
    allowPrivacy: (t = r(e().mark((function r() {
      return e().wrap((function(e) {
        for (;;) switch (e.prev = e.next) {
          case 0:
            return e.abrupt("return", ((0, u.saveCache)("userAgreement", "", !0), (0, a.flushLocalEvents)(), (0, n.allowPrivacy)()));
          case 1:
          case "end":
            return e.stop()
        }
      }), r)
    }))), function() {
      return t.apply(this, arguments)
    })
  }
});