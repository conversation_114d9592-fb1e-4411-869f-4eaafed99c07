var e = require("../@babel/runtime/helpers/classCallCheck"),
  t = require("../@babel/runtime/helpers/createClass"),
  r = require("../@babel/runtime/helpers/assertThisInitialized"),
  n = require("../@babel/runtime/helpers/inherits"),
  a = require("../@babel/runtime/helpers/createSuper"),
  s = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var l = function(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}(require("./EventTarget"));
var o = new WeakMap,
  i = new WeakMap,
  u = new WeakMap;

function c(e) {
  var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  "function" == typeof this["on".concat(e)] && this["on".concat(e)].call(this, s(s({}, t), {}, {
    target: t.target || this
  }))
}

function d(e) {
  var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  this.readyState = e, c.call(this, "readystatechange", s(s({}, t), {}, {
    readyState: e
  }))
}

function h(e) {
  return !/^(http|https|ftp|wxfile):\/\/.*/i.test(e)
}
var p = function(l) {
  n(f, l);
  var p = a(f);

  function f() {
    var t;
    return e(this, f), (t = p.call(this)).onabort = null, t.onerror = null, t.onload = null, t.onloadstart = null, t.onprogress = null, t.ontimeout = null, t.onloadend = null, t.onreadystatechange = null, t.readyState = 0, t.response = null, t.responseText = null, t.responseType = "text", t.dataType = "string", t.responseXML = null, t.status = 0, t.statusText = "", t.upload = {}, t.withCredentials = !1, o.set(r(t), {
      "content-type": "application/x-www-form-urlencoded"
    }), i.set(r(t), {}), t
  }
  return t(f, [{
    key: "abort",
    value: function() {
      var e = u.get(this);
      e && e.abort()
    }
  }, {
    key: "getAllResponseHeaders",
    value: function() {
      var e = i.get(this);
      return Object.keys(e).map((function(t) {
        return "".concat(t, ": ").concat(e[t])
      })).join("\n")
    }
  }, {
    key: "getResponseHeader",
    value: function(e) {
      return i.get(this)[e]
    }
  }, {
    key: "open",
    value: function(e, t) {
      this.method = e, this.url = t, d.call(this, f.OPENED)
    }
  }, {
    key: "overrideMimeType",
    value: function() {}
  }, {
    key: "send",
    value: function() {
      var e = this,
        t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
      if (this.readyState !== f.OPENED) throw new Error("Failed to execute 'send' on 'XMLHttpRequest': The object's state must be OPENED.");
      var r, n = this.url,
        a = this.responseType,
        s = this.dataType,
        l = o.get(this),
        u = h(n);
      "arraybuffer" === a || (r = "utf8"), delete this.response, this.response = null;
      var p = function(t) {
          var r = t.data,
            n = t.statusCode,
            a = t.header,
            s = void 0 === n ? 200 : n,
            l = r;
          if ("string" != typeof r && !(r instanceof ArrayBuffer)) try {
            l = JSON.stringify(r)
          } catch (n) {
            l = r
          }
          e.status = s, a && i.set(e, a), c.call(e, "loadstart"), d.call(e, f.HEADERS_RECEIVED), d.call(e, f.LOADING), e.response = l, l instanceof ArrayBuffer ? Object.defineProperty(e, "responseText", {
            enumerable: !0,
            configurable: !0,
            get: function() {
              throw "InvalidStateError : responseType is ".concat(this.responseType)
            }
          }) : e.responseText = l, d.call(e, f.DONE), c.call(e, "load"), c.call(e, "loadend")
        },
        v = function(t) {
          var r = t.errMsg; - 1 !== r.indexOf("abort") ? c.call(e, "abort") : c.call(e, "error", {
            message: r
          }), c.call(e, "loadend")
        };
      if (u) {
        var y = wx.getFileSystemManager(),
          g = {
            filePath: n,
            success: p,
            fail: v
          };
        return r && (g.encoding = r), void y.readFile(g)
      }
      wx.request({
        data: t,
        url: n,
        method: this.method,
        header: l,
        dataType: s,
        responseType: a,
        success: p,
        fail: v
      })
    }
  }, {
    key: "setRequestHeader",
    value: function(e, t) {
      var r = o.get(this);
      r[e] = t, o.set(this, r)
    }
  }, {
    key: "addEventListener",
    value: function(e, t) {
      var r = this;
      "function" == typeof t && (this["on".concat(e)] = function() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        t.call(r, s(s({}, e), {}, {
          target: e.target || r
        }))
      })
    }
  }, {
    key: "removeEventListener",
    value: function(e, t) {
      this["on".concat(e)] === t && (this["on".concat(e)] = null)
    }
  }]), f
}(l.default);
exports.default = p, p.UNSEND = 0, p.OPENED = 1, p.HEADERS_RECEIVED = 2, p.LOADING = 3, p.DONE = 4;