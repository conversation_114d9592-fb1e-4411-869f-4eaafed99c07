var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.getCategorySharing = function() {
  return p.apply(this, arguments)
}, exports.getCategoryTree = function(e) {
  return c.apply(this, arguments)
}, exports.getMenuCategories = function() {
  return u.apply(this, arguments)
}, exports.getSubMenuCategories = function(e) {
  return a.apply(this, arguments)
};
var t = require("./common"),
  n = "/onemp";

function u() {
  return (u = r(e().mark((function r() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", t.wechatApiInst.get("".concat(n, "/menu_categories/v1")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function a() {
  return (a = r(e().mark((function r(u) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", t.wechatApiInst.post("".concat(n, "/sub_menu_categories/v1"), {
            id: u
          }));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function c() {
  return (c = r(e().mark((function r(u) {
    var a, c, p;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return a = u.productTypeId, c = u.pageNum, p = u.pageSize, e.prev = 1, e.abrupt("return", t.wechatApiInst.get("".concat(n, "/category/tree/v1?productTypeId=").concat(a, "&pageNum=").concat(c, "&pageSize=").concat(p)));
        case 5:
          return e.prev = 5, e.t0 = e.catch(1), e.abrupt("return", Promise.reject(e.t0));
        case 8:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [1, 5]
    ])
  })))).apply(this, arguments)
}

function p() {
  return (p = r(e().mark((function r() {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.abrupt("return", t.wechatApiInst.get("".concat(n, "/category/sharing/v1")));
        case 4:
          return e.prev = 4, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 7:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}