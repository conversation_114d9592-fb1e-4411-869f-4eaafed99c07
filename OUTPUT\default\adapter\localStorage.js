Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = {
  get length() {
    return wx.getStorageInfoSync().keys.length
  },
  key: function(e) {
    return wx.getStorageInfoSync().keys[e]
  },
  getItem: function(e) {
    var t = wx.getStorageSync(e);
    return "" === t ? null : t
  },
  setItem: function(e, t) {
    return global.asyncStorage ? wx.setStorage({
      key: e,
      data: t
    }) : wx.setStorageSync(e, t)
  },
  removeItem: function(e) {
    return global.asyncStorage ? wx.removeStorage({
      key: e
    }) : wx.removeStorageSync(e)
  },
  clear: function() {
    return global.asyncStorage ? wx.clearStorage() : wx.clearStorageSync()
  }
};
exports.default = e;