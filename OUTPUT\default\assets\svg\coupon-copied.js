Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var n = require("../../utils/common");
exports.default = function() {
  return (0, n.svgToBase64)('<?xml version="1.0" encoding="utf-8"?>\n<svg version="1.1" id="'.concat(Date.now(), '" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"\n\t viewBox="0 0 48 48" style="enable-background:new 0 0 48 48;" xml:space="preserve">\n<style type="text/css">\n\t.st0{fill:#111111;}\n\t.st1{fill:#FFFFFF;}\n</style>\n<defs>\n     <clipPath id="tickContainer">\n        <rect x="3" y="4" width="0" height="7">\n            <animate\n                attributeName="width"\n                from="0"\n                to="8"\n                keyTimes="0;1"\n                dur="0.6s"\n                begin="1.2s"\n                repeatCount="1"\n                calcMode="spline"\n                keySplines="0.34, 0, 0.64, 1"\n                fill="freeze"\n            />\n        </rect>\n    </clipPath>\n</defs>\n<g transform="translate(3, 8)">\n<path class="st0" d="M15.5,6.2c1,0,1.9,0.3,2.6,0.9c0.7-0.5,1.6-0.9,2.6-0.9h18.1c1.9,0,3.5,1.5,3.5,3.5v16.4\n\tc0,1.9-1.5,3.5-3.5,3.5H20.7c-1,0-1.9-0.3-2.6-0.9c-0.7,0.5-1.6,0.9-2.6,0.9h-6C7.5,29.5,6,28,6,26.1V9.7c0-1.9,1.5-3.5,3.5-3.5\n\tH15.5z M15.5,8.9h-6c-0.4,0-0.7,0.3-0.7,0.6l0,0.1v16.4c0,0.4,0.3,0.7,0.6,0.7l0.1,0h6c0.3,0,0.6-0.1,0.8-0.2l0.2-0.1\n\tc0.1-0.1,0.2-0.1,0.3-0.2l0-0.7c0-0.7,0.6-1.3,1.3-1.3s1.3,0.6,1.3,1.3l0,0.7c0,0,0.1,0,0.1,0.1l0.2,0.2c0.2,0.2,0.5,0.3,0.8,0.3\n\tl0.2,0h18.1c0.4,0,0.7-0.3,0.7-0.6l0-0.1V9.7c0-0.4-0.3-0.7-0.6-0.7l-0.1,0H20.7c-0.3,0-0.6,0.1-0.8,0.2l-0.2,0.1\n\tc-0.1,0.1-0.2,0.1-0.3,0.2l0,0.7c0,0.7-0.6,1.3-1.3,1.3s-1.3-0.6-1.3-1.3l0-0.7c0,0-0.1,0-0.1-0.1l-0.2-0.2\n\tc-0.2-0.2-0.5-0.3-0.8-0.3L15.5,8.9z M26.8,11.3l2.5,2.5l2.5-2.5c0.5-0.5,1.3-0.5,1.8,0c0.5,0.5,0.5,1.3,0,1.8l-2.5,2.5h1.6\n\tc0.7,0,1.2,0.5,1.3,1.2l0,0.1c0,0.7-0.5,1.2-1.2,1.3l-0.1,0h-2.2v1.3h2.2c0.7,0,1.2,0.5,1.3,1.2l0,0.1c0,0.7-0.5,1.2-1.2,1.3l-0.1,0\n\th-2.2v1.5c0,0.7-0.5,1.2-1.2,1.3l-0.1,0c-0.7,0-1.2-0.5-1.3-1.2l0-0.1v-1.5h-2.2c-0.7,0-1.2-0.5-1.3-1.2l0-0.1\n\tc0-0.7,0.5-1.2,1.2-1.3l0.1,0H28v-1.3h-2.2c-0.7,0-1.2-0.5-1.3-1.2l0-0.1c0-0.7,0.5-1.2,1.2-1.3l0.1,0h1.6L25,13.2\n\tc-0.5-0.5-0.5-1.3,0-1.8C25.5,10.8,26.3,10.8,26.8,11.3z M18.1,15.3c0.7,0,1.3,0.6,1.3,1.3v2.6c0,0.7-0.6,1.3-1.3,1.3\n\ts-1.3-0.6-1.3-1.3v-2.6C16.8,15.9,17.4,15.3,18.1,15.3z"/>\n<circle class="st0" cx="7" cy="7.2" r="0">\n    <animate\n        attributeName="r"\n        from="0"\n        to="7"\n        keyTimes="0;1"\n        dur="1s"\n        begin="0.2s"\n        repeatCount="1"\n        fill="freeze"\n        calcMode="spline"\n        keySplines="0.34, 0, 0.64, 1"\n    />\n</circle>\n<path id="Path" class="st1" d="M9.2,4.6c0.3-0.3,0.8-0.3,1.1,0c0.3,0.3,0.3,0.7,0.1,1l-0.1,0.1L6.5,9.4c-0.3,0.3-0.8,0.3-1.1,0\n\tL5.3,9.4L3.7,7.6c-0.3-0.3-0.3-0.8,0-1.1c0.3-0.3,0.7-0.3,1,0l0.1,0.1l1.1,1.1L9.2,4.6z" clip-path="url(#tickContainer)" />\n</g>\n</svg>'))
};