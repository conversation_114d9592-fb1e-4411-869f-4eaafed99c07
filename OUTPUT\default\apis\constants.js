Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.WECHAT_ENTERPRISE_PLUS_BETA_AGENT_ID = exports.WECHAT_ENTERPRISE_NIKE_PLUS_AGENT_ID = exports.WECHAT_ENTERPRISE_CORP_ID = exports.WECHAT_CN_API_TEST_URL = exports.WECHAT_CN_API_TEST_HOST = exports.WECHAT_CN_API_BASE_URL = exports.WECHAT_CN_API_BASE_HOST = exports.UNITE_API_BASEURL = exports.NIKE_ID_IMAGE = exports.NIKE_API_IMAGE = exports.NIKE_API_BASE_URL = exports.MAR_TECH_API_TEST_URL = exports.MAR_TECH_API_BASE_URL = exports.HEADER_CALLER_ID = exports.EDA_API_TEST_URL = exports.EDA_API_BASE_URL = exports.COMMON_HEADER = void 0;
exports.NIKE_API_BASE_URL = "https://api.nike.com.cn", exports.UNITE_API_BASEURL = "https://unite.nike.com", exports.WECHAT_CN_API_TEST_URL = "https://wechat-test.nike.com.cn", exports.WECHAT_CN_API_BASE_URL = "https://wechat.nike.com.cn", exports.WECHAT_CN_API_TEST_HOST = "wechat-test.nike.com.cn", exports.WECHAT_CN_API_BASE_HOST = "wechat.nike.com.cn", exports.COMMON_HEADER = {
  Accept: "application/json",
  "Content-Type": "application/json; charset=UTF-8"
}, exports.HEADER_CALLER_ID = {
  "nike-api-caller-id": "nike:wechat:web:1.0"
}, exports.NIKE_API_IMAGE = "https://images.nike.com.cn/is/image/DotCom/", exports.NIKE_ID_IMAGE = "https://secure-nikeid.nike.com/services/imgredirect/", exports.WECHAT_ENTERPRISE_CORP_ID = "ww68b4548bcf17451a", exports.WECHAT_ENTERPRISE_NIKE_PLUS_AGENT_ID = "1000002", exports.WECHAT_ENTERPRISE_PLUS_BETA_AGENT_ID = "1000087", exports.MAR_TECH_API_TEST_URL = "https://api.adtech-test.nikecloud.com.cn", exports.MAR_TECH_API_BASE_URL = "https://api.adtech-prod.nikecloud.com.cn", exports.EDA_API_TEST_URL = "https://z6lonhicw7.execute-api.cn-northwest-1.amazonaws.com.cn/stage1/main", exports.EDA_API_BASE_URL = "https://wechat-order-intake.eda-gc.nikecloud.com.cn";