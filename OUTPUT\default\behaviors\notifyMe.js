require("../@babel/runtime/helpers/Objectentries");
var e = require("../@babel/runtime/helpers/slicedToArray"),
  t = require("../@babel/runtime/helpers/defineProperty"),
  r = require("../@babel/runtime/helpers/objectSpread2"),
  n = require("../@babel/runtime/helpers/regeneratorRuntime"),
  i = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var a = l(require("../utils/configureStore")),
  u = require("../state/global-components"),
  s = require("../utils/global-components-config"),
  c = l(require("../lib/mp")),
  o = require("../apis/notifyMe");

function l(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
exports.default = Behavior({
  data: {
    top3Subjects: [],
    toBeSubscribed: []
  },
  methods: {
    initSubscription: function(e) {
      var t = this;
      return i(n().mark((function r() {
        var i, a, u, s, c;
        return n().wrap((function(r) {
          for (;;) switch (r.prev = r.next) {
            case 0:
              return i = e.slice(0, 3), t.setData({
                top3Subjects: i,
                toBeSubscribed: i
              }), a = [], u = [], i.forEach((function(e) {
                var t = e.type,
                  r = e.id;
                a.push(t), u.push(r)
              })), r.next = 6, (0, o.inquireV2)(a, u);
            case 6:
              s = r.sent, c = i.filter((function(e) {
                var t, r = e.type,
                  n = e.id;
                return !(null != s && null !== (t = s.data) && void 0 !== t && t.some((function(e) {
                  var t = e.subjectType,
                    i = e.subjectId;
                  return r === t && n === i
                })))
              })), t.setData({
                toBeSubscribed: c
              });
            case 9:
            case "end":
              return r.stop()
          }
        }), r)
      })))()
    },
    subscribeNotification: function() {
      var s = this;
      return i(n().mark((function l() {
        var p, d, f, b;
        return n().wrap((function(l) {
          for (;;) switch (l.prev = l.next) {
            case 0:
              if (0 !== (p = s.data.toBeSubscribed).length) {
                l.next = 3;
                break
              }
              return l.abrupt("return");
            case 3:
              return d = p.reduce((function(e, n) {
                return r(r({}, e), {}, t({}, n.templateId, {
                  originalDestinationUrl: n.originalDestinationUrl,
                  type: n.type,
                  id: n.id
                }))
              }), {}), l.prev = 4, l.next = 7, c.default.getSetting({
                withSubscriptions: !0
              });
            case 7:
              if (f = l.sent, b = f.subscriptionsSetting, (void 0 === b ? {} : b).mainSwitch) {
                l.next = 12;
                break
              }
              return l.abrupt("return", (s.notificationErrorHandler(), !1));
            case 12:
              l.next = 16;
              break;
            case 14:
              l.prev = 14, l.t0 = l.catch(4);
            case 16:
              c.default.requestSubscribeMessage({
                tmplIds: p.map((function(e) {
                  return e.templateId
                })),
                success: function() {
                  var t = i(n().mark((function t(r) {
                    var c;
                    return n().wrap((function(t) {
                      for (;;) switch (t.prev = t.next) {
                        case 0:
                          if (c = [], Object.entries(r).forEach((function(t) {
                              var r = e(t, 2),
                                n = r[0],
                                i = r[1];
                              "errMsg" !== n && "accept" === i && c.push(n)
                            })), t.t0 = c.length > 0, !t.t0) {
                            t.next = 7;
                            break
                          }
                          return s.triggerTraversalEvent("notifyMeAccept", {
                            destinationUrl: d[c[0]].originalDestinationUrl,
                            acceptedTypes: c.map((function(e) {
                              return d[e].type
                            })).join(",")
                          }), t.next = 7, s.triggerEvent("startLoadingTask", {
                            showScreenMask: !1,
                            fn: function() {
                              var e = i(n().mark((function e() {
                                var t, r, i;
                                return n().wrap((function(e) {
                                  for (;;) switch (e.prev = e.next) {
                                    case 0:
                                      return t = c.map((function(e) {
                                        return {
                                          subjectType: d[e].type,
                                          subjectId: d[e].id
                                        }
                                      })), r = [], e.prev = 2, e.next = 5, (0, o.subscribeV2)(t);
                                    case 5:
                                      i = e.sent, r = null == i ? void 0 : i.data, e.next = 11;
                                      break;
                                    case 9:
                                      e.prev = 9, e.t0 = e.catch(2);
                                    case 11:
                                      r.length > 0 && (a.default.dispatch((0, u.showToast)({
                                        title: "订阅成功"
                                      })), s.triggerTraversalEvent("notifyMeSucceed", {
                                        succeededTypes: r.map((function(e) {
                                          return e.subjectType
                                        })).join(",")
                                      })), s.setData({
                                        toBeSubscribed: p.filter((function(e) {
                                          var t = e.type,
                                            n = e.id;
                                          return !r.some((function(e) {
                                            var r = e.subjectType,
                                              i = e.subjectId;
                                            return t === r && n === i
                                          }))
                                        }))
                                      });
                                    case 12:
                                    case "end":
                                      return e.stop()
                                  }
                                }), e, null, [
                                  [2, 9]
                                ])
                              })));
                              return function() {
                                return e.apply(this, arguments)
                              }
                            }()
                          }, {
                            bubbles: !0,
                            composed: !0
                          });
                        case 7:
                        case "end":
                          return t.stop()
                      }
                    }), t)
                  })));
                  return function(e) {
                    return t.apply(this, arguments)
                  }
                }(),
                fail: function(e) {
                  20004 === e.errCode && s.notificationErrorHandler()
                }
              });
            case 17:
            case "end":
              return l.stop()
          }
        }), l, null, [
          [4, 14]
        ])
      })))()
    },
    notificationErrorHandler: function() {
      var e = this;
      a.default.dispatch((0, u.showModal)(r(r({}, s.MODAL_NOTIFICATION_MAIN_SWITCH_OFF), {}, {
        enableModalCancelHandler: !0,
        enableModalConfirmHandler: !0,
        handleModalCancel: function() {
          a.default.dispatch((0, u.hideModal)())
        },
        handleModalConfirm: function() {
          wx.openSetting({
            withSubscriptions: !0,
            success: function() {
              setTimeout((function() {
                e.triggerEvent("backFromSetting")
              }), 300)
            },
            complete: function() {
              a.default.dispatch((0, u.hideModal)())
            }
          })
        }
      })))
    }
  }
});