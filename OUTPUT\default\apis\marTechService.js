var r = require("../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.sendAddOrderApiEventToMarTech = function(r) {
  return n.apply(this, arguments)
}, exports.sendUpdateOrderApiEventToMarTech = function(r) {
  return u.apply(this, arguments)
};
var t = require("./common");

function n() {
  return (n = e(r().mark((function e(n) {
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return r.prev = 0, r.abrupt("return", t.marTechApiInst.post("api/v1/add_order", n));
        case 4:
          return r.prev = 4, r.t0 = r.catch(0), r.abrupt("return", Promise.reject(r.t0));
        case 7:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}

function u() {
  return (u = e(r().mark((function e(n) {
    return r().wrap((function(r) {
      for (;;) switch (r.prev = r.next) {
        case 0:
          return r.prev = 0, r.abrupt("return", t.marTechApiInst.post("api/v1/update_order", n));
        case 4:
          return r.prev = 4, r.t0 = r.catch(0), r.abrupt("return", Promise.reject(r.t0));
        case 7:
        case "end":
          return r.stop()
      }
    }), e, null, [
      [0, 4]
    ])
  })))).apply(this, arguments)
}