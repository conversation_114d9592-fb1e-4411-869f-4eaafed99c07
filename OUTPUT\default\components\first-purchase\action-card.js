var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = d(require("../../utils/configureStore")),
  s = require("../../vendor/redux"),
  n = require("../../state/firstPurchase"),
  a = require("../../state/user"),
  i = require("../../utils/coupons"),
  u = require("../../utils/analytics/core"),
  c = d(require("../../behaviors/context")),
  o = require("../../lib/mp-redux/index");

function d(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var l = {
    options: {
      addGlobalClass: !0
    },
    behaviors: [c.default],
    properties: {
      loggedIn: {
        type: Boolean,
        value: !1
      },
      title: {
        type: String,
        value: ""
      },
      subtitle: {
        type: String,
        value: ""
      },
      actions: {
        type: Array,
        value: []
      }
    },
    ready: function() {},
    methods: {
      onClaim: function() {
        var s = this;
        return t(e().mark((function a() {
          var c, o, d;
          return e().wrap((function(a) {
            for (;;) switch (a.prev = a.next) {
              case 0:
                return a.next = 2, s.triggerEvent("startLoadingTask", {
                  fn: function() {
                    var r = t(e().mark((function t() {
                      return e().wrap((function(e) {
                        for (;;) switch (e.prev = e.next) {
                          case 0:
                            return e.next = 2, (0, i.claimCoupon)({
                              stockId: s.data.stockId,
                              customeRuleLimitCb: function() {
                                s.setClaimed()
                              },
                              customeClaimSucceedCb: function() {
                                setTimeout((function() {
                                  s.setClaimed()
                                }), 3500), (0, u.sendTrackEvent)("Memberstore_1St_Purchase_Card_Claim_Success")
                              }
                            });
                          case 2:
                          case "end":
                            return e.stop()
                        }
                      }), t)
                    })));
                    return function() {
                      return r.apply(this, arguments)
                    }
                  }()
                }, {
                  bubbles: !0,
                  composed: !0
                });
              case 2:
                return a.next = 4, s.getContextData();
              case 4:
                c = a.sent, o = c.gender, d = (0, n.getFirstPurchaseThread)(r.default.getState()), (0, u.sendTrackEvent)("Memberstore_1St_Purchase_Card_Claim", {
                  gender: o,
                  threadId: d.threadId,
                  url: d.cta.url
                });
              case 8:
              case "end":
                return a.stop()
            }
          }), a)
        })))()
      },
      onDismiss: function() {
        var s = this;
        return t(e().mark((function t() {
          var a, i, c;
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return s.dismissFirstPurchase(), e.next = 3, s.getContextData();
              case 3:
                a = e.sent, i = a.gender, c = (0, n.getFirstPurchaseThread)(r.default.getState()), (0, u.sendTrackEvent)("Memberstore_1St_Purchase_Card_Skip", {
                  gender: i,
                  threadId: c.threadId
                });
              case 7:
              case "end":
                return e.stop()
            }
          }), t)
        })))()
      }
    }
  },
  h = {
    ensureAuth: a.ensureAuth,
    setClaimed: n.setClaimed,
    dismissFirstPurchase: n.dismissFirstPurchase
  };
(0, s.compose)(Component, (0, o.connectComponent)(r.default)((function(e) {
  return {
    isShow: (0, n.isFirstPurchaseSkipped)(e) && (0, n.isFirstPurchaseActive)(e) && (0, n.isUserQualified)(e),
    stockId: (0, n.getFirstPurchaseStockId)(e)
  }
}), h))(l);