var t = require("../../../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.normalizeCartPromotionReviewResponse = function(t) {
  var o;
  return null != t && null !== (o = t.data) && void 0 !== o && o.totals ? {
    data: {
      nextTierPromotions: t.data.totals.nextTierPromotions
    }
  } : {
    data: {}
  }
}, exports.normalizeCartReviewResponse = function(a) {
  if (null == a || !a.data) return {
    data: {}
  };
  var e = a.data,
    r = e.fulfillmentGroups,
    n = e.currency,
    i = e.locale,
    u = [];
  return r.forEach((function(t) {
    t.items.forEach((function(t) {
      var a = t.itemCosts,
        e = t.quantity;
      a.promotionDiscounts && a.promotionDiscounts.forEach((function(t) {
        if (t.amount) {
          var a = {
            amount: Math.round(t.amount * e * 100) / 100,
            formattedAmount: (0, o.formatPriceForDisplay)(t.amount * e, n, i),
            displayName: t.displayName,
            code: t.code
          };
          u.push(a)
        }
      }))
    }))
  })), a.data.promotionDiscounts = u, {
    data: t(t({}, a.data), {}, {
      fulfillmentGroups: r.map((function(o) {
        return {
          items: o.items.map((function(o) {
            return t(t({}, o), o.itemCosts)
          }))
        }
      }))
    })
  }
};
var o = require("../../../utils/data-format");