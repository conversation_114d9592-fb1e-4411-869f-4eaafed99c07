<template name="toast">
    <view class="toast-container {{hideAnimationClass?hideAnimationClass:''}}" wx:if="{{isShowingToast}}">
        <view class="toast-background toast-background--{{toastOptions.theme}}"></view>
        <view class="toast toast--{{toastOptions.theme}} {{toastOptions.className||''}}" style="width: {{toastOptions.width||'auto'}}; background: {{toastOptions.backgroundColor||''}}; color: {{toastOptions.titleColor||''}}; font-weight: {{toastOptions.titleWeight||''}}; padding: {{toastOptions.padding||''}};">
            <image src="{{toastOptions.image}}" wx:if="{{toastOptions.image}}"></image>
            <view wx:if="{{toastOptions.title}}">
                <text>{{toastOptions.title}}</text>
            </view>
        </view>
    </view>
</template>
<template name="loading">
    <view class="toast-container toast-container-{{isLoadingFadingOut?'fading':''}}" wx:if="{{(isShowingLoading||isLoadingFadingOut)&&!isShowingToast}}">
        <view class="toast-background" style="background: {{loadingOptions.maskBackground||'transparent'}};"></view>
        <view class="toast loading-toast {{loadingOptions.title?'toast-with-title':''}}">
            <image class="image-1" src="/assets/svg/loading_swoosh.svg"></image>
            <image class="image-2" src="/assets/svg/loading_swoosh_orange.svg"></image>
            <view class="text text-0" wx:if="{{loadingOptions.title}}">{{loadingOptions.title}}</view>
            <view class="text text-1" wx:if="{{!loadingOptions.title}}">再等等，精彩马上呈现</view>
            <view class="text text-2" wx:if="{{!loadingOptions.title}}">再坚持下，马上就好</view>
        </view>
    </view>
    <view class="timeoutToast g-flex-center fl-column" wx:if="{{isShowingTimeoutToast}}">
        <view class="toastPopup">
            <view class="popupHeader g-flex-center fl-column">
                <text class="popupHeaderTitle">似乎有点问题？</text>
                <text class="popupHeaderDesc">加载有问题？你可以尝试退出当前页面过会再返回试试；或者尝试刷新</text>
            </view>
            <view class="popupFooter">
                <view bind:tap="{{timeoutOptions.dismiss}}" class="g-flex-center" hoverClass="press-down">好的</view>
                <view bind:tap="{{timeoutOptions.reload}}" class="g-flex-center" hoverClass="press-down">刷新</view>
            </view>
        </view>
    </view>
    <view class="timeoutError" wx:if="{{isShowingTimeoutError}}">
        <view class="top">
            <image mode="widthFix" src="https://mp-static-assets.gc.nike.com/image/svg/icon-not-found.svg"></image>
            <view class="timeoutErrorInfo">连接超时</view>
            <view class="timeoutErrorContent">您的网络连接出现了问题</view>
        </view>
        <view bind:tap="{{timeoutOptions.reload}}" class="timeoutRefreshBtn">刷新</view>
    </view>
</template>
<template name="fullscreenTip">
    <view class="fullscreenTip-container g-flex-center" style="background-color: {{fullScreenTipOptions.backgroundColor}}" wx:if="{{isShowingFullScreenTip}}">
        <view class="fullscreenTip">
            <image src="{{fullScreenTipOptions.image}}" wx:if="{{fullScreenTipOptions.image}}"></image>
            <view wx:if="{{fullScreenTipOptions.title}}">{{fullScreenTipOptions.title}}</view>
        </view>
    </view>
</template>
<template name="modal">
    <view catch:touchmove="onTouchModalContainer" class="modal-container g-flex-center" style="background-color: rgba(0,0,0,{{modalOptions.bgOpacity===undefined?0.7:modalOptions.bgOpacity}})" wx:if="{{isShowingModal}}">
        <view class="modal">
            <image bind:tap="{{modalOptions.enableModalCloseHandler?'handleModalClose':modalOptions.cancelBtnHandlerName}}" class="modal-close-btn" data-type="{{modalOptions.type}}" src="https://mp-static-assets.gc.nike.com/image/shop/close_btn.svg" wx:if="{{modalOptions.isShowingCloseBtn}}"></image>
            <view class="modal-content {{modalOptions.className||''}}">
                <image class="modal-content-top-image" mode="widthFix" src="{{modalOptions.topIcon}}" style="width: {{modalOptions.topIconWidth}}px; margin-bottom: 8px" wx:if="{{modalOptions.topIcon}}"></image>
                <view class="modal-content-title">{{modalOptions.title}}</view>
                <rich-text class="modal-content-detail" nodes="{{modalOptions.detail}}"></rich-text>
                <view class="modal-content-section" wx:if="{{modalOptions.extraSections==='giftCardBarScanner'}}">
                    <view class="gift-card-bar-scanner-container">
                        <image src="https://mp-static-assets.gc.nike.com/image/shop/gift-card-bar-scanner.png"></image>
                        <view class="gift-card-bar-scanner-line"></view>
                    </view>
                </view>
            </view>
            <view class="modal-btns thin-border-top {{modalOptions.className||''}}" wx:if="{{!modalOptions.openType}}">
                <view bind:tap="{{modalOptions.enableModalCancelHandler?'handleModalCancel':modalOptions.cancelHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" wx:if="{{modalOptions.isShowingCancelBtn}}">{{modalOptions.cancelText}}</view>
                <view bind:tap="{{modalOptions.enableModalConfirmHandler?'handleModalConfirm':modalOptions.confirmHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" wx:if="{{modalOptions.isShowingConfirmBtn}}">
                    <image class="modal-btn-icon" mode="widthFix" src="{{modalOptions.btnIcon}}" style="width: {{modalOptions.btnIconWidth||20}}px" wx:if="{{modalOptions.btnIcon}}"></image>{{modalOptions.confirmText}}</view>
            </view>
            <view class="modal-btns modal-getPhoneNumber thin-border-top" wx:elif="{{modalOptions.openType==='getPhoneNumber'}}">
                <button bind:getphonenumber="getPhoneNumber" bind:tap="{{modalOptions.enableModalCancelHandler?'handleModalCancel':modalOptions.cancelHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" openType="getPhoneNumber" wx:if="{{modalOptions.isShowingCancelBtn}}">{{modalOptions.cancelText}}</button>
                <view bind:tap="{{modalOptions.enableModalConfirmHandler?'handleModalConfirm':modalOptions.confirmHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" wx:if="{{modalOptions.isShowingConfirmBtn}}">{{modalOptions.confirmText}}</view>
            </view>
            <view class="modal-btns thin-border-top" wx:elif="{{modalOptions.openType==='share'}}">
                <button bind:tap="{{modalOptions.enableModalCancelHandler?'handleModalCancel':modalOptions.cancelHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" wx:if="{{modalOptions.isShowingCancelBtn}}">{{modalOptions.cancelText}}</button>
                <button bind:tap="{{modalOptions.enableModalConfirmHandler?'handleModalConfirm':modalOptions.confirmHandlerName}}" class="modal-btn" data-type="{{modalOptions.type}}" openType="share" wx:if="{{modalOptions.isShowingConfirmBtn}}">{{modalOptions.confirmText}}</button>
            </view>
        </view>
    </view>
</template>
<template name="tooltip">
    <view catch:touchmove="onTouchMove" class="tooltip_container" style="{{backgroundColor||''}}" wx:if="{{isShow}}">
        <view bind:tap="{{!isNoMaskClose?onClose:''}}" class="mask" wx:if="{{!isNoMask}}"></view>
        <view bind:tap="{{!isNoMaskClose?onClose:''}}" class="mask highlight" style="height: {{maskHighlight.height}}px;width: {{maskHighlight.width}}px; border-top-width: {{maskHighlight.top}}px; border-left-width: {{maskHighlight.left}}px; border-right-width: {{maskHighlight.right}}px; border-bottom-width: {{maskHighlight.bottom}}px;" wx:if="{{maskHighlight&&isNoMask}}">
            <view class="mask-border" style="height: {{maskHighlight.height}}px;width: {{maskHighlight.width}}px;"></view>
        </view>
        <view class="wrapper animate-slide-{{arrow}}" style="{{arrow}}:{{arrow==='top'?positionY+40:positionY-40}}px;">
            <view class="triangle-{{arrow}}" style="left:{{positionX}}px;"></view>
            <view class="wrap-content">
                <view bind:tap="{{onClose}}" class="modal-close-btn" wx:if="{{isShowingCloseBtn}}"></view>
                <view class="content">
                    <view class="content-title">{{title}}</view>
                    <view class="content-desc">{{content}}</view>
                    <view class="content-button" wx:if="{{btnLabel}}">
                        <button bind:tap="{{onClose}}" class="btn btn--small btn--light" hoverClass="press-down-button">{{btnLabel}}</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<template name="dot-options">
    <view class="dot-options g-flex g-items-center">
        <view class="dot dot-small"></view>
        <view class="dot dot-large"></view>
        <view class="dot dot-small"></view>
    </view>
</template>
<template name="tooltip-pin-notice">
    <view bind:tap="{{onClose}}" catch:touchmove="onTouchMove" class="tooltip_container tooltip-pin-notice {{isShowPinNotice?'shown':''}}" style="{{backgroundColor||''}}">
        <view class="wrapper {{isShowPinNotice?'animate-slide-bottom2':'animate-slide-bottom2-hide'}}" style="top: {{positionY-40}}px;">
            <view class="triangle-top {{isShowPinNotice?'slide-up':''}}" style="right: 56rpx"></view>
            <view class="wrap-content">
                <view class="content" style="padding:40rpx 50rpx;">
                    <view class="g-flex" style="justify-content: space-between;position:relative;">
                        <view>
                            <view class="f-21" style="line-height:1;margin-bottom:14px;">添加到我的小程序</view>
                            <view class="g-flex g-items-center c-9a f-13" style="line-height:18px;">
                                <text>点击</text>
                                <view style="margin:0 9px;">
                                    <template is="dot-options" data></template>
                                </view>
                                <text>后，选择</text>
                            </view>
                            <view class="c-9a f-13" style="line-height:18px;">
                                <text>“添加到我的小程序”即可</text>
                            </view>
                        </view>
                        <image mode="aspectFit" src="{{iconUrl}}"></image>
                        <label bind:tap="{{onClose}}" class="icon icon-close" style="padding:4px;margin:-4px;"></label>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<template name="emailDOBPopup">
    <view catchtouchmove class="email-popup" wx:if="{{isShowingEmailDOBPopup}}">
        <view catch:tap="hideEmailDOBPopup" class="bg animate-fade-in" hidden="{{emailDOBPopupConfigs.isFadingOut}}"></view>
        <view class="email-panel {{emailDOBPopupConfigs.isFadingOut?'animate-popup-out':'animate-popup-in'}} {{isDeviceSupportCustomTabbar&&hasTabbar&&'skipTabbar'||''}} {{isIPhoneX&&'iphoneX'||''}}">
            <view catch:tap="hideEmailDOBPopup" class="icon-collapse {{emailDOBPopupConfigs.isFadingOut?'animate-spin-out':''}}"></view>
            <view class="popup-email-section" style="margin-bottom: {{emailDOBPopupConfigs.mode==='Email'?30:60}}rpx" wx:if="{{emailDOBPopupConfigs.mode!=='DOB'}}">
                <view>
                    <text>电子邮件</text>
                </view>
                <view class="input-container">
                    <input bindblur="updateEmailDOBPopup" bindinput="updateEmailDOBPopup" class="input-email {{emailDOBPopupConfigs.emailValidationErrorType===''||emailDOBPopupConfigs.emailValidationErrorType==='coppa'?'':'error-border'}}" cursorSpacing="50" data-key="Email" type="text" value="{{emailDOBPopupConfigs.tempEmail}}"></input>
                    <view class="error tip" wx:if="{{emailDOBPopupConfigs.emailValidationErrorType==='invalid-format'}}">请输入有效的邮箱地址</view>
                    <view class="error tip" wx:if="{{emailDOBPopupConfigs.emailValidationErrorType==='empty-value'}}">邮箱地址不能为空</view>
                    <view class="error tip" wx:if="{{emailDOBPopupConfigs.emailValidationErrorType==='conflict'}}">电子邮箱已被占用</view>
                </view>
            </view>
            <view class="popup-dob-section" wx:if="{{emailDOBPopupConfigs.mode!=='Email'}}">
                <view>
                    <text>出生日期</text>
                </view>
                <view class="input-container">
                    <picker bindchange="updateEmailDOBPopup" data-key="DOB" end="{{emailDOBPopupConfigs.birthRangeEnd}}" mode="date" start="{{emailDOBPopupConfigs.birthRangeStart}}" value="{{emailDOBPopupConfigs.tempDOBText}}">
                        <view class="dob-value {{emailDOBPopupConfigs.emailValidationErrorType==='coppa'?'error':''}}">
                            <span>{{emailDOBPopupConfigs.tempDOBText}}</span>
                            <text class="dob-value-placeholder" wx:if="{{!emailDOBPopupConfigs.tempDOBText}}">请输入你的出生日期</text>
                        </view>
                    </picker>
                </view>
                <view class="error tip" wx:if="{{emailDOBPopupConfigs.emailValidationErrorType==='coppa'}}">输入的生日显示你的年龄不满年龄限制</view>
            </view>
            <view class="tip general-tip" style="margin-top: {{emailDOBPopupConfigs.mode==='both'?60:30}}rpx">{{emailDOBPopupConfigs.mode==='Email'?'请绑定你的电子邮件，这个电子邮件将保存到你的账户信息。':emailDOBPopupConfigs.mode==='DOB'?'请输入你的出生日期，这个出生日期将保存到你的账户信息。':'请输入你的电子邮件和出生日期，这些信息将保存到你的账户。'}}</view>
            <view class="cta {{isIPhoneX?'cta--iphoneX':''}}">
                <button bindtap="submitEmail" class="btn {{(emailDOBPopupConfigs.mode!=='DOB'&&(emailDOBPopupConfigs.emailValidationErrorType!==''||!emailDOBPopupConfigs.tempEmail)||emailDOBPopupConfigs.mode==='DOB'&&!emailDOBPopupConfigs.tempDOB)&&'btn--disabled'}}" disabled="{{emailDOBPopupConfigs.mode!=='DOB'&&(emailDOBPopupConfigs.emailValidationErrorType!==''||!emailDOBPopupConfigs.tempEmail)||emailDOBPopupConfigs.mode==='DOB'&&!emailDOBPopupConfigs.tempDOB}}" hoverClass="press-down-button" plain="true">确认</button>
            </view>
        </view>
    </view>
</template>
