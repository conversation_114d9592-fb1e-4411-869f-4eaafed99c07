var e, t = require("../../@babel/runtime/helpers/objectSpread2"),
  r = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../../@babel/runtime/helpers/defineProperty"),
  i = require("../../lib/dot-prop"),
  s = require("../../utils/common"),
  o = require("../../utils/pageTransition"),
  u = require("../../constants/page-route"),
  c = g(require("../../vendor/qs")),
  d = require("../../vendor/redux"),
  p = require("../../lib/mp-redux/index"),
  l = g(require("../../utils/configureStore")),
  h = require("../../state/user"),
  f = require("../../utils/customer-service");

function g(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var v = 9,
  b = 22,
  m = (a(e = {}, u.ROUTE_PDP, ["stylecolor", "styleColor"]), a(e, u.ROUTE_FEED_THREAD, ["threadId", "threadid"]), e),
  y = {
    options: {
      addGlobalClass: !0
    },
    properties: {
      enableIndicator: {
        type: Boolean,
        value: !1
      },
      btnClass: {
        type: String,
        value: ""
      },
      indicatorClass: {
        type: String,
        value: ""
      },
      pressClass: {
        type: String,
        value: "press-down-button"
      },
      isMemberGated: {
        type: Boolean,
        value: !0,
        observer: "_onIsMemberGatedChange"
      },
      action: {
        type: Object,
        observer: "_onActionChange"
      },
      chatContext: {
        type: String,
        value: "",
        observer: "_onChatContextChange"
      },
      btnLabel: {
        type: String,
        value: "立即预约"
      },
      message: {
        type: Object,
        value: void 0
      }
    },
    data: {
      openId: "",
      isOpening: !1
    },
    attached: function() {
      var e = this;
      return n(r().mark((function t() {
        var n;
        return r().wrap((function(t) {
          for (;;) switch (t.prev = t.next) {
            case 0:
              return e.checkUserStatus(), t.next = 3, (0, h.getOpenIdResolver)(l.default.dispatch, l.default.getState);
            case 3:
              n = t.sent, e.setData({
                openId: n
              });
            case 5:
            case "end":
              return t.stop()
          }
        }), t)
      })))()
    },
    lifetimes: {
      ready: function() {
        var e = (0, s.getCurrentPagesWrapper)(),
          t = (0, i.get)(e, "" + (e.length - 1)),
          r = t.options,
          n = t.route,
          a = "/".concat(n, "?").concat(c.default.stringify(r));
        this.setData({
          path: a,
          route: n,
          query: r
        })
      }
    },
    pageLifetimes: {
      show: function() {
        this.checkUserStatus()
      }
    },
    methods: {
      isPDPPage: function(e) {
        return u.ROUTE_PDP.indexOf(e) > -1
      },
      checkIsOpening: function() {
        var e = (new Date).getHours();
        v <= e && e < b ? this.setData({
          isOpening: !0
        }) : this.setData({
          isOpening: !1
        })
      },
      checkUserStatus: function() {
        var e = this;
        return n(r().mark((function t() {
          var n, a;
          return r().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                if (n = e.data, a = n.isMemberGated, n.enableIndicator && e.checkIsOpening(), !a) {
                  t.next = 11;
                  break
                }
                return t.prev = 2, t.next = 5, l.default.dispatch((0, h.ensureAuth)());
              case 5:
                e.setData({
                  isLoggedin: !0
                }), t.next = 11;
                break;
              case 8:
                t.prev = 8, t.t0 = t.catch(2), e.setData({
                  isLoggedin: !1
                });
              case 11:
              case "end":
                return t.stop()
            }
          }), t, null, [
            [2, 8]
          ])
        })))()
      },
      onTapLogin: function() {
        l.default.dispatch((0, h.ensureLoggedIn)("/pages/auth/member-benefits")), this.triggerEvent("onTapCSLogin", {
          action: this.data.action
        }, {
          composed: !0,
          bubbles: !0
        })
      },
      onTapCS: function() {
        var e = this;
        return n(r().mark((function t() {
          var n;
          return r().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                n = e.data.openId, (0, f.bindMemberWithCSSystem)(n), e.triggerEvent("onTapCS", {
                  action: e.data.action
                }, {
                  composed: !0,
                  bubbles: !0
                });
              case 2:
              case "end":
                return t.stop()
            }
          }), t)
        })))()
      },
      isSamePageURL: function(e) {
        var t = this.data.route;
        return e.indexOf(t) > -1
      },
      shouldRedirect: function(e, t) {
        var r = m[e],
          n = [];
        if (r) {
          var a = this.data.query;
          n = r.filter((function(e) {
            return t[e] && a[e] && t[e] === a[e]
          }))
        }
        return 0 === n.length
      },
      handleContact: function(e) {
        var t = e.detail,
          r = t.path,
          n = t.query,
          a = {},
          i = "";
        if (u.ROUTE_REDIRECT.indexOf(r) > -1) {
          var s = n._target;
          i = (0, o.mapPageAliasToPath)(s).split("?")[0], Object.keys(n).forEach((function(e) {
            e.startsWith("_") || (a[e] = n[e])
          }))
        } else a = n, i = r;
        this.shouldRedirect(i, a) && (0, o.goToPageWithPathAndParams)(i, a, this.isSamePageURL(i))
      },
      _onChatContextChange: function(e, r) {
        var n = this.data,
          a = n.route,
          i = n.query;
        if (e && r && this.isPDPPage(a)) {
          var s = JSON.parse(e).userContext.find((function(e) {
            return "styleColor" === e.k
          }));
          this.setData({
            path: "/".concat(a, "?stylecolor=").concat(s.v),
            query: t(t({}, i), {}, {
              stylecolor: s.v
            })
          })
        }
      }
    }
  };
var C = {
  ensureAuth: h.ensureAuth
};
(0, d.compose)(Component, (0, p.connectComponent)(l.default)((function(e) {
  return {
    umpId: (0, h.getUpmId)(e)
  }
}), C, {
  setDataHook: function(e) {
    var t = this;
    setTimeout((function() {
      e.umpId && t.checkUserStatus()
    }))
  }
}))(y);