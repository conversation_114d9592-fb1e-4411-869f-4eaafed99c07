var e = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var t = require("../utils/pageTransition"),
  a = require("../constants/page-alias");
exports.default = Behavior({
  methods: {
    handleNavigation: function(e, t) {
      var a = e.target.dataset.destination,
        r = e.currentTarget.dataset.destination;
      this.omniNavigate(a || r, t)
    },
    omniNavigate: function(r, i) {
      if (r) switch (r.type) {
        case "URL":
          var o = r.url;
          if ((0, t.isExternalLink)(o)) {
            var n = (0, t.convertAppUrlToInternalUrl)(o).internalUrl;
            n && (0, t.goToPageWithInternalUrl)(n, i)
          } else o.startsWith(t.INTERNAL_URL_PREFIX) && (o.startsWith("".concat(t.INTERNAL_URL_PREFIX).concat(a.PAGE_ALIAS_LIVEPLAYER)) && (o = o.replace("roomid", "room_id")), i ? (0, t.goToPageWithInternalUrl)(o, i) : (0, t.goToPageWithInternalUrl)(o));
          break;
        case "PDP":
          var s = r.styleColor,
            l = i || {};
          (0, t.goToPageWithAliasUrlAndParams)(a.PAGE_ALIAS_PDP, e({
            styleColor: s
          }, l))
      }
    }
  }
});