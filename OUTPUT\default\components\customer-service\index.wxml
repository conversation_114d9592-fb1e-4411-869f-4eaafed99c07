<view class="{{indicatorClass}}" wx:if="{{enableIndicator&&isOpening}}"></view>
<button bindtap="onTapLogin" class="{{btnClass}}" hoverClass="{{pressClass}}" wx:if="{{isMemberGated&&!isLoggedin}}">{{btnLabel}}</button>
<button bindcontact="handleContact" bindtap="onTapCS" class="{{btnClass}}" disabled="{{openId?false:true}}" hoverClass="{{pressClass}}" openType="contact" sendMessageImg="{{message.img}}" sendMessagePath="{{message.path||path}}" sendMessageTitle="{{message.title}}" sessionFrom="{{chatContext}}" showMessageCard="{{!!message}}" wx:else>{{btnLabel}}</button>
