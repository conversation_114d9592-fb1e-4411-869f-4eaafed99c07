.container {
    background-color: #fff;
    border: .5px solid rgba(0,0,0,.02);
    border-radius: 10px;
    box-shadow: 0 10px 15px 0 rgba(0,0,0,.1);
    box-sizing: border-box;
    line-height: 100rpx;
    padding: 0 40rpx;
    transition-property: height,margin-top,opacity,-webkit-transform;
    transition-property: height,margin-top,opacity,transform;
    transition-property: height,margin-top,opacity,transform,-webkit-transform
}

.container.hide {
    height: 0;
    margin-top: 0;
    opacity: 0;
    -webkit-transform: scale(.8,.9);
    transform: scale(.8,.9);
    transition-delay: .1s,.1s,0s,0s
}

.container.hide,.container.show {
    transition-duration: .3s,.3s,.3s,.3s
}

.container.show {
    height: 100rpx;
    margin-top: 94rpx;
    opacity: 1;
    -webkit-transform: scale(1,1);
    transform: scale(1,1);
    transition-delay: 0s,0s,.2s,.2s
}

.container .close-icon {
    height: 34rpx;
    position: absolute;
    right: 34rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 34rpx
}
