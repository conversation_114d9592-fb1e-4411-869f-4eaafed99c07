var e = require("../@babel/runtime/helpers/typeof"),
  r = function(r, t) {
    if ("function" == typeof WeakMap) var n = new WeakMap,
      o = new WeakMap;
    return function(r, t) {
      if (!t && r && r.__esModule) return r;
      var a, i, l = {
        __proto__: null,
        default: r
      };
      if (null === r || "object" != e(r) && "function" != typeof r) return l;
      if (a = t ? o : n) {
        if (a.has(r)) return a.get(r);
        a.set(r, l)
      }
      for (var u in r) "default" !== u && {}.hasOwnProperty.call(r, u) && ((i = (a = Object.defineProperty) && Object.getOwnPropertyDescriptor(r, u)) && (i.get || i.set) ? a(l, u, i) : l[u] = r[u]);
      return l
    }(r, t)
  }(require("./window"));
global.wx = wx, Object.keys(r).forEach((function(e) {
  global[e] = r[e]
})), global.window = global;