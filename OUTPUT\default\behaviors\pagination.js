var e = require("../@babel/runtime/helpers/objectSpread2"),
  t = require("../@babel/runtime/helpers/toConsumableArray"),
  a = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.feedPagination = exports.default = void 0;
var n = require("../apis/common"),
  i = {
    properties: {
      anchor: {
        type: Number,
        value: 0
      },
      count: {
        type: Number,
        value: 50
      },
      timeoutRetry: {
        type: Boolean,
        value: !0
      },
      timeout: {
        type: Number,
        value: 1e4
      },
      scrollId: {
        type: String,
        value: ""
      },
      loadMoreExternalTrigger: {
        type: Number,
        value: -1,
        observer: function(e, t) {
          -1 !== t && this.loadNextPage()
        }
      }
    },
    data: {
      data: [],
      loading: !1,
      nextPage: null
    },
    definitionFilter: function(e) {
      if ("function" == typeof e.methods.loadFirstPage) {
        var t = e.methods.loadFirstPage;
        e.methods.loadFirstPage = r(a().mark((function e() {
          var r, n, i, o;
          return a().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return this.setData({
                  loading: !0
                }), e.prev = 1, e.next = 4, t.call(this);
              case 4:
                r = e.sent, n = r.data, i = n.objects, o = n.pages.next, this.setData({
                  nextPage: o,
                  data: "function" == typeof this.formatDataFn ? this.formatDataFn(i) : i
                });
              case 9:
                return e.prev = 9, this.setData({
                  loading: !1
                }), e.finish(9);
              case 12:
              case "end":
                return e.stop()
            }
          }), e, this, [
            [1, , 9, 12]
          ])
        })))
      }
    },
    methods: {
      loadNextPage: function() {
        var e = this;
        return r(a().mark((function r() {
          var i, o, s, u, l, c, d, p, f, h, m;
          return a().wrap((function(a) {
            for (;;) switch (a.prev = a.next) {
              case 0:
                if (i = e.data, o = i.nextPage, s = i.data, u = e.properties, l = u.timeoutRetry, c = u.timeout, !o) {
                  a.next = 15;
                  break
                }
                return e.setData({
                  loading: !0
                }), a.prev = 3, a.next = 6, (0, n.nikeApiInst)({
                  timeoutRetry: l
                }).get(o, {
                  timeout: c
                });
              case 6:
                d = a.sent, p = d.data, f = p.objects, h = p.pages.next, m = "function" == typeof e.formatDataFn ? e.formatDataFn(f) : f, e.setData({
                  nextPage: h,
                  data: [].concat(t(s), t(m))
                });
              case 12:
                return a.prev = 12, e.setData({
                  loading: !1
                }), a.finish(12);
              case 15:
              case "end":
                return a.stop()
            }
          }), r, null, [
            [3, , 12, 15]
          ])
        })))()
      }
    }
  };
exports.default = Behavior(i), exports.feedPagination = Behavior(e(e({}, i), {}, {
  definitionFilter: function(e) {
    if ("function" == typeof e.methods.loadFirstPage) {
      var t = e.methods.loadFirstPage;
      e.methods.loadFirstPage = r(a().mark((function e() {
        var r, n, i, o, s;
        return a().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return this.setData({
                loading: !0
              }), e.prev = 1, e.next = 4, t.call(this);
            case 4:
              r = e.sent, n = r.data, i = n.objects, o = n.pages.next, s = "function" == typeof this.formatDataFn ? this.formatDataFn(i) : i, this.setData({
                nextPage: o,
                data: s
              }), s.length <= 3 && this.loadNextPage();
            case 10:
              return e.prev = 10, this.setData({
                loading: !1
              }), e.finish(10);
            case 13:
            case "end":
              return e.stop()
          }
        }), e, this, [
          [1, , 10, 13]
        ])
      })))
    }
  }
}));