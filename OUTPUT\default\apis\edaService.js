var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = function(e) {
  return n.apply(this, arguments)
};
var t = require("./common");

function n() {
  return (n = r(e().mark((function r(n) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.abrupt("return", t.edaApiInst.post("", n));
        case 1:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}