var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var r = require("../state/global-components"),
  a = u(require("../utils/configureStore")),
  n = require("../utils/global-components-config"),
  o = require("../utils/image-helper"),
  s = u(require("../lib/mp"));

function u(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
exports.default = Behavior({
  methods: {
    saveImageToAlbum: function(s) {
      var u = this;
      return t(e().mark((function i() {
        var l;
        return e().wrap((function(i) {
          for (;;) switch (i.prev = i.next) {
            case 0:
              return l = s.target.dataset.url, u.triggerEvent("saveTapped"), i.next = 4, u.triggerEvent("startLoadingTask", {
                fn: function() {
                  var s = t(e().mark((function s() {
                    return e().wrap((function(s) {
                      for (;;) switch (s.prev = s.next) {
                        case 0:
                          wx.getImageInfo({
                            src: (0, o.getImageUrlforICP)(l),
                            success: function() {
                              var r = t(e().mark((function t(r) {
                                return e().wrap((function(e) {
                                  for (;;) switch (e.prev = e.next) {
                                    case 0:
                                      return e.next = 2, u.saveImageToAlbumCore(r.path);
                                    case 2:
                                    case "end":
                                      return e.stop()
                                  }
                                }), t)
                              })));
                              return function(e) {
                                return r.apply(this, arguments)
                              }
                            }(),
                            fail: function(e) {
                              if ("saveImageToPhotosAlbum:fail:auth denied" === e.errMsg || "saveImageToPhotosAlbum:fail auth deny" === e.errMsg || "saveImageToPhotosAlbum:fail authorize no response" === e.errMsg) throw a.default.dispatch((0, r.showModal)(n.MODAL_AUTH_WRITE_PHOTO_ALBUM_CONF)), new Error(e);
                              a.default.dispatch((0, r.showModal)({
                                title: "图片下载失败！请重试！",
                                isShowingCloseBtn: !1,
                                isShowingCancelBtn: !1
                              }))
                            }
                          });
                        case 1:
                        case "end":
                          return s.stop()
                      }
                    }), s)
                  })));
                  return function() {
                    return s.apply(this, arguments)
                  }
                }()
              }, {
                bubbles: !0,
                composed: !0
              });
            case 4:
            case "end":
              return i.stop()
          }
        }), i)
      })))()
    },
    savePathToAlbum: function(r) {
      var a = this;
      return t(e().mark((function t() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.next = 2, a.saveImageToAlbumCore(r);
            case 2:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    },
    saveImageToAlbumCore: function(o) {
      var u = this;
      return t(e().mark((function t() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              return e.prev = 0, e.next = 3, s.default.saveImageToPhotosAlbum({
                filePath: o
              });
            case 3:
              u.data.disableToastAfterSave || a.default.dispatch((0, r.showToast)({
                title: "已保存到相册"
              })), e.next = 9;
              break;
            case 6:
              e.prev = 6, e.t0 = e.catch(0), "saveImageToPhotosAlbum:fail:auth denied" === e.t0.errMsg || "saveImageToPhotosAlbum:fail auth deny" === e.t0.errMsg || "saveImageToPhotosAlbum:fail authorize no response" === e.t0.errMsg ? a.default.dispatch((0, r.showModal)(n.MODAL_AUTH_WRITE_PHOTO_ALBUM_CONF)) : a.default.dispatch((0, r.showModal)({
                title: "图片下载失败！请重试！",
                isShowingCloseBtn: !1,
                isShowingCancelBtn: !1
              }));
            case 9:
            case "end":
              return e.stop()
          }
        }), t, null, [
          [0, 6]
        ])
      })))()
    }
  }
});