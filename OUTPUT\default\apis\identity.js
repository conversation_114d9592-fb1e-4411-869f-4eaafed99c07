Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.sendCode = function(t) {
  var n = getApp().globalData.appConfig.accounts.uxId;
  return (0, e.nikeApiInst)({
    timeoutRetry: !1
  }).post("/identity/verify/contact_channel/initiation/v1", {
    contactChannel: "+86".concat(t)
  }, {
    header: {
      "x-nike-ux-id": n,
      "accept-language": "zh-Hans"
    }
  })
}, exports.validateCode = function(t, n) {
  var a = getApp().globalData.appConfig.accounts.uxId;
  return (0, e.nikeApiInst)({
    timeoutRetry: !1
  }).post("/identity/verify/contact_channel/completion/v1", {
    contactChannel: "+86".concat(t),
    verificationCode: n
  }, {
    header: {
      "x-nike-ux-id": a,
      "Accept-Language": "zh-Hans"
    }
  })
};
var e = require("./common");