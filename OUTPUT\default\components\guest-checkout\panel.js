var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../utils/analytics/tracker"),
  n = require("../../utils/analytics/core"),
  i = require("../../utils/mpEnhancers"),
  a = require("../../utils/analytics/constants"),
  o = require("../../state/app"),
  s = h(require("../../behaviors/socialLogin")),
  u = require("../../state/cms"),
  c = require("../../vendor/redux"),
  l = require("../../lib/mp-redux/index"),
  d = h(require("../../utils/configureStore"));

function h(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var p = {
  behaviors: [s.default],
  options: {
    addGlobalClass: !0
  },
  properties: {
    isOpen: {
      type: <PERSON><PERSON><PERSON>,
      observer: function(n) {
        return t(e().mark((function t() {
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                n && (0, r.sendSimpleTrackEvent)("Guest_Gate_Shows");
              case 1:
              case "end":
                return e.stop()
            }
          }), t)
        })))()
      }
    },
    isMemberExclusive: {
      type: Boolean
    }
  },
  data: {},
  pageLifetimes: {
    ready: function() {},
    hide: function() {},
    show: function() {}
  },
  methods: {
    hidePanel: function() {
      this.triggerEvent("close")
    },
    startGuestCheckout: function() {
      (0, r.sendSimpleTrackEvent)("Guest_Gate_Checkout"), this.triggerEvent("guest", {}, {
        bubbles: !0,
        composed: !0
      })
    },
    navToLogin: function(e) {
      var t;
      "join" === (null === (t = e.currentTarget.dataset) || void 0 === t ? void 0 : t.view) ? (0, n.sendTrackEvent)(a.TRACK_JOIN_US_CLICKED, {
        pageName: (0, i.getPageName)(),
        threadName: "",
        threadId: ""
      }) : (0, n.sendTrackEvent)(a.TRACK_SIGN_IN_CLICKED, {
        pageName: (0, i.getPageName)(),
        threadName: "",
        threadId: ""
      }), this.afterGetUserProfile(e)
    }
  }
};
(0, c.compose)(Component, (0, l.connectComponent)(d.default)((function(e) {
  return {
    isIPhoneX: (0, o.isDeviceIPhoneX)(e),
    guestCheckoutConfig: (0, u.getGuestCheckoutConfig)(e)
  }
})))(p);