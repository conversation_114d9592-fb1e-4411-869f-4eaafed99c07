var e = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.findThreads = n, exports.findThreadsByCollectionIds = function(t) {
  var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    i = r.filter || {};
  return i["publishedContent.properties.publish.collections"] = t.join(","), n(e(e({}, r), {}, {
    filter: i,
    direct: 1 === t.length
  }))
}, exports.findThreadsByFeedId = function(t) {
  var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    i = r.filter || {};
  return i.feedId = t, n(e(e({}, r), {}, {
    filter: i
  }))
}, exports.findThreadsByIdList = function(t) {
  var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    i = r.filter || {};
  return i.id = t.join(","), n(e(e({}, r), {}, {
    filter: i,
    direct: 1 === t.length
  }))
}, exports.findThreadsByStoreIds = function(t) {
  var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    i = r.filter || {};
  return i.restrictedContent = t.join(","), n(e(e({}, r), {}, {
    filter: i,
    direct: 1 === t.length
  }))
};
var t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs")),
  r = require("./common"),
  i = require("../utils/constants");

function n() {
  var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    o = n.filter,
    d = void 0 === o ? {} : o,
    s = n.query,
    l = void 0 === s ? {} : s,
    u = n.timeoutInMilliseconds,
    c = n.timeoutRetry,
    a = n.direct,
    f = void 0 === a || a,
    h = e({
      marketplace: "CN",
      language: "zh-Hans",
      channelId: i.CHANNEL_ID_OTHER_THREAD
    }, d),
    v = t.default.stringify(e(e({
      filter: Object.keys(h).map((function(e) {
        return "".concat(e, "(").concat(h[e], ")")
      }))
    }, l), {}, {
      fields: f ? void 0 : l.fields
    }), {
      indices: !1
    });
  return f ? (0, r.nikeApiInst)({
    timeoutRetry: c
  }).get("/product_feed/threads/v3?".concat(v), {
    timeout: u,
    withoutToken: !0
  }) : r.wechatApiInst.get("/product_feed/threads/v2?".concat(v), {
    timeout: u,
    withoutToken: !0
  })
}