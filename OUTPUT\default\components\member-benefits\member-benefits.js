var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = b(require("../../behaviors/context")),
  n = require("../../constants/thread"),
  a = require("../../utils/pageTransition"),
  o = require("../../constants/page-alias"),
  i = require("../../utils/analytics/constants"),
  s = require("../../constants/page-route"),
  l = require("../../utils/common"),
  u = require("../../state/firstPurchase"),
  c = require("../../vendor/redux"),
  d = b(require("../../utils/configureStore")),
  h = b(require("../../lib/mp")),
  f = require("../../lib/mp-redux/index"),
  g = require("../../state/app"),
  m = require("../../state/cms"),
  p = require("../../state/user"),
  v = require("../../state/global-components"),
  C = require("../../utils/analytics/core");

function b(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}
var I = {
    options: {
      addGlobalClass: !0
    },
    behaviors: [r.default],
    properties: {
      navBottom: {
        type: String,
        value: "130rpx"
      },
      height: {
        type: String,
        value: "100vh"
      },
      contentMode: {
        type: String,
        value: "memberWall"
      },
      nextPageUrl: {
        type: String,
        value: ""
      },
      isMemberwall: {
        type: Boolean,
        value: !1
      }
    },
    data: {
      assetsHost: getApp().globalData.appConfig.assetsHost,
      colorTheme: "light",
      autoPlay: !0,
      interval: 2e3,
      imageIndex: 0,
      memberWallImages: [],
      logoOpacity: 1,
      showLoginJoinPanel: !1,
      isUserInfoAuthorized: !1
    },
    phoneCode: null,
    methods: {
      onSaveFormId: function() {
        var r = this;
        return t(e().mark((function t() {
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return e.next = 2, r.loadOpenId();
              case 2:
              case "end":
                return e.stop()
            }
          }), t)
        })))()
      },
      _startLoading: function() {
        (0, C.sendTrackEvent)("Join_Nikeplus")
      },
      _handleConnectClick: function(r) {
        var n = this;
        return t(e().mark((function a() {
          return e().wrap((function(a) {
            for (;;) switch (a.prev = a.next) {
              case 0:
                r.detail, (0, C.sendTrackEvent)(i.TRACK_JOIN_NIKEPLUS_CLICKED, {
                  pageName: i.PAGE_NAME_MEMBER_BENEFITS,
                  destinationValue: n.data.nextPageUrl ? s.ROUTE_ACCOUNTS_DEEPLINK : ""
                }), n.data.nextPageUrl || d.default.dispatch((0, p.setCheckEmailDOBAfterLogin)({
                  value: !1,
                  url: ""
                })), n.triggerEvent("startLoadingTask", {
                  fn: function() {
                    var r = t(e().mark((function t() {
                      return e().wrap((function(e) {
                        for (;;) switch (e.prev = e.next) {
                          case 0:
                            n.joinOrLogin();
                          case 1:
                          case "end":
                            return e.stop()
                        }
                      }), t)
                    })));
                    return function() {
                      return r.apply(this, arguments)
                    }
                  }()
                }, {
                  bubbles: !0,
                  composed: !0
                });
              case 2:
              case "end":
                return a.stop()
            }
          }), a)
        })))()
      },
      joinOrLogin: function() {
        return t(e().mark((function t() {
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return e.next = 2, h.default.navigateTo({
                  url: s.ROUTE_ACCOUNTS_DEEPLINK
                });
              case 2:
              case "end":
                return e.stop()
            }
          }), t)
        })))()
      },
      _updateTheme: function(e) {
        var t = this.data,
          r = t.memberWallImages,
          n = t.memberIntroImages,
          a = (e && e.detail ? e.detail : {
            current: 0
          }).current,
          o = "memberIntro" === this.data.contentMode ? n : r;
        if ((null == o ? void 0 : o.length) > 0) {
          var i = a !== o.length - 1,
            u = o[a].colorTheme,
            c = (0, l.getCurrentPagesWrapper)();
          if (this.setData({
              imageIndex: a,
              colorTheme: u,
              autoPlay: i
            }), 1 === a && this.setData({
              interval: 5e3
            }), "/".concat(c[c.length - 1].route) !== s.ROUTE_LOGIN || "/".concat(c[c.length - 1].route) !== s.ROUTE_HOME) return;
          "light" === u ? wx.setNavigationBarColor({
            frontColor: "#ffffff",
            backgroundColor: "#000000"
          }) : wx.setNavigationBarColor({
            frontColor: "#000000",
            backgroundColor: "#ffffff"
          })
        }
      },
      updateTitleOpacity: function(e) {
        this.setData({
          logoOpacity: e
        })
      },
      handleCloseJoinOrLoginPanel: function() {
        this.setData({
          showLoginJoinPanel: !1
        })
      },
      onFirstPurchaseRuleInfo: function() {
        var e = this;
        this.data.isFirstPurchaseActive && this.showModal({
          title: this.data.threadContent.ruleInfoDialog.title,
          detail: this.data.threadContent.ruleInfoDialog.body,
          confirmText: "知道了",
          cancelText: "查看规则",
          isShowingCloseBtn: !1,
          enableModalCancelHandler: !0,
          enableModalConfirmHandler: !0,
          handleModalCancel: function() {
            (0, a.goToPageWithAliasUrlAndParams)(o.PAGE_ALIAS_FEED_THREAD, {
              threadId: n.FIRST_PURCHASE_RULE_THREAD_ID
            }), e.triggerTraversalEvent("1stPurchaseInfoModalSeeDetail")
          },
          handleModalConfirm: function() {
            e.hideModal(), e.triggerTraversalEvent("1stPurchaseInfoModalClose")
          }
        }), this.triggerTraversalEvent("1stPurchaseInfoMarkTap")
      }
    },
    attached: function() {
      var r = this;
      return t(e().mark((function t() {
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if ("memberIntro" !== r.data.contentMode) {
                e.next = 5;
                break
              }
              return e.next = 3, r.fetchContentMemberIntro();
            case 3:
              e.next = 7;
              break;
            case 5:
              return e.next = 7, r.fetchContentMemberWall();
            case 7:
              r._updateTheme(), (0, l.getCurrentPagesWrapper)();
            case 9:
            case "end":
              return e.stop()
          }
        }), t)
      })))()
    }
  },
  E = {
    showModal: v.showModal,
    hideModal: v.hideModal,
    fetchContentMemberWall: m.fetchContentMemberWall,
    fetchContentMemberIntro: m.fetchContentMemberIntro,
    loadOpenId: p.loadOpenId
  };
(0, c.compose)(Component, (0, f.connectComponent)(d.default)((function(e) {
  return {
    memberWallImages: (0, m.getMemberWall)(e),
    memberIntroImages: (0, m.getMemberIntro)(e),
    globalComponents: (0, v.getGlobalComponents)(e),
    statusBarHeight: (0, g.getStatusBarHeight)(e),
    isFirstPurchaseActive: (0, u.isFirstPurchaseActive)(e),
    threadContent: (0, u.getFirstPurchaseThread)(e)
  }
}), E))(I);