Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var e = require("../state/global-components"),
  t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../utils/configureStore")),
  a = require("../utils/global-components-config");
exports.default = Behavior({
  data: {
    cellShow: !0
  },
  methods: {
    completemessage: function(r) {
      var o = this;
      if (r && r.errcode) {
        var s = "";
        switch (r.errcode) {
          case -3002:
            s = "获取插件配置信息失败";
            break;
          case -3004:
            s = "用户信息授权失败";
            break;
          case -3005:
            s = "消息发送失败";
            break;
          case -3009:
            s = "群聊已满员";
            break;
          case -3010:
            s = "群聊已解散";
            break;
          case -3011:
            s = "用户命中企业群聊黑名单";
            break;
          case -3012:
            s = "用户已在群里中且群已满员"
        }
        s && t.default.dispatch((0, e.showToast)((0, a.getGeneralErrorToast)(s)))
      }
      this.setData({
        cellShow: !1
      }), setTimeout((function() {
        o.setData({
          cellShow: !0
        })
      }), 100)
    }
  }
});