var e = require("../@babel/runtime/helpers/classCallCheck"),
  t = require("../@babel/runtime/helpers/createClass");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0;
var r = new WeakMap,
  i = function() {
    function i() {
      e(this, i), r.set(this, {})
    }
    return t(i, [{
      key: "addEventListener",
      value: function(e, t) {
        var i = r.get(this);
        i || (i = {}, r.set(this, i)), i[e] || (i[e] = []), i[e].push(t)
      }
    }, {
      key: "removeEventListener",
      value: function(e, t) {
        var i = r.get(this);
        if (i) {
          var a = i[e];
          if (a && a.length > 0)
            for (var s = a.length; s--; s > 0)
              if (a[s] === t) {
                a.splice(s, 1);
                break
              }
        }
      }
    }, {
      key: "dispatchEvent",
      value: function() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
          t = r.get(this)[e.type];
        if (t)
          for (var i = 0; i < t.length; i++) t[i](e)
      }
    }]), i
  }();
exports.default = i;