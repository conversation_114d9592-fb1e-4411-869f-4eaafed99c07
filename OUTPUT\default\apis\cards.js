var e = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.findCardsByConceptIds = o;
var t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs")),
  r = require("./common"),
  n = require("../utils/constants");

function i() {
  var i = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    o = i.filter,
    u = void 0 === o ? {} : o,
    c = i.query,
    a = void 0 === c ? {} : c,
    d = i.timeoutInMilliseconds,
    s = i.timeoutRetry,
    l = e({
      marketplace: "CN",
      language: "zh-Hans",
      channelId: n.CHANNEL_ID_WECHAT
    }, u),
    f = t.default.stringify(e({
      filter: Object.keys(l).map((function(e) {
        return "".concat(e, "(").concat(l[e], ")")
      }))
    }, a), {
      indices: !1
    });
  return (0, r.nikeApiInst)({
    timeoutRetry: s
  }).get("/product_feed/cards/v2?".concat(f), {
    timeout: d
  })
}

function o(t) {
  var r = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
    n = r.filter || {};
  return n.conceptIds = t.join(","), i(e(e({}, r), {}, {
    filter: n
  }))
}
exports.default = o;