Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.filterStores = function() {}, exports.getNearByStores = function(t) {
  var o = t.radiusInKm,
    i = t.longitude,
    a = t.latitude,
    r = t.timeoutRetry,
    n = void 0 === r || r,
    c = getApp().globalData.appConfig,
    l = c.nikeApiCallerId,
    s = c.nikeApiBaseurl,
    u = 'coordinates=geoProximity={"maxDistance": '.concat(Math.floor(o) + 1, ', "measurementUnits": "km", "latitude": ').concat(Number(a).toFixed(2), ', "longitude": ').concat(Number(i).toFixed(2), "} and (facilityType =='NIKE_OWNED_STORE' or facilityType == 'MONO_BRAND_NON_FRANCHISEE_PARTNER_STORE' or facilityType=='FRANCHISEE_PARTNER_STORE')"),
    d = "".concat(s, "/store/store_locations/v1?language=zh&search=(").concat(encodeURIComponent(u), ")");
  return (0, e.nikeApiInst)({
    timeoutRetry: n
  }).get(d, {
    header: {
      "nike-api-caller-id": l
    }
  })
};
var e = require("./common");