var e = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.getInvites = i;
var t = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs")),
  n = require("./common"),
  r = require("../utils/constants");

function i() {
  var i = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    a = i.filters,
    o = void 0 === a ? {} : a,
    u = i.query,
    s = void 0 === u ? {} : u,
    c = i.timeoutRetry,
    d = i.timeoutInMilliseconds,
    l = e(e({}, o), {}, {
      marketplace: "CN",
      language: "zh-Hans",
      channelId: r.CHANNEL_ID_OTHER_THREAD
    }),
    p = getApp().globalData.appConfig.accounts,
    f = p.uxId,
    v = t.default.stringify(e({
      filter: Object.keys(l).map((function(e) {
        return "".concat(e, "(").concat(l[e], ")")
      }))
    }, s), {
      indices: !1
    });
  return (0, n.nikeApiInst)({
    timeoutRetry: c
  }).get("/engage/invites/v1?".concat(v), {
    header: {
      appid: f
    },
    timeout: d
  }).then((function(e) {
    return e.data
  }))
}
exports.default = i;