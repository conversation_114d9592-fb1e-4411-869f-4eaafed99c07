Component({
  properties: {
    pointX: {
      type: Number,
      value: 0
    },
    pointY: {
      type: Number,
      value: 100
    },
    vHeight: {
      type: Number,
      value: 0
    },
    vWidth: {
      type: Number,
      value: 0
    },
    yTopEdge: {
      type: Number,
      value: 100
    },
    yBottomEdge: {
      type: Number,
      value: 120
    }
  },
  data: {},
  attached: function() {
    var e = wx.getSystemInfoSync(),
      t = e.screenHeight,
      i = e.screenWidth;
    this.screenHeight = t, this.screenWidth = i
  },
  methods: {
    setPageXY: function(e, t) {
      var i = this.data,
        h = i.vHeight,
        s = i.vWidth,
        n = e - s / 2,
        a = t - h / 2,
        o = n,
        r = a;
      n < 5 ? o = 5 : n > this.screenWidth - s && (o = this.screenWidth - s - 2);
      var c = this.data,
        u = c.yTopEdge,
        d = c.yBottomEdge;
      a < u ? r = u : a > this.screenHeight - h - d && (r = this.screenHeight - d - h), this.setData({
        pointX: o,
        pointY: r
      })
    },
    touchmove: function(e) {
      var t = e.changedTouches[0],
        i = t.clientX,
        h = t.clientY;
      this.setPageXY(i, h)
    }
  }
});