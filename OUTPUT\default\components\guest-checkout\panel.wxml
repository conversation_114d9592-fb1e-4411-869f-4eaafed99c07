<panel bgZIndex="202" bind:close="hidePanel" id="guest-panel" isOpen="{{isOpen}}">
    <view bindtap="hidePanel" class="panel-header g-flex g-items-center">
        <view class="panel-header__title g-flex g-items-center" style="font-size: 18px">{{isMemberExclusive?'登录会员购买':guestCheckoutConfig.title}}</view>
        <view class="dash"></view>
    </view>
    <view style="padding-bottom: {{isIPhoneX?203:194}}px" wx:if="{{!isMemberExclusive}}">
        <view class="ol">
            <view class="li" wx:for="{{guestCheckoutConfig.body}}" wx:key="item">{{item}}</view>
        </view>
    </view>
    <view class="member-exclusive" style="padding-bottom: {{isIPhoneX?177:164}}px" wx:else>此商品为会员限定商品。立即成为 Nike 会员获得发售商品购买权。</view>
    <view class="cta cta-guest cta--fixed {{isIPhoneX?'cta--iphoneX':''}}">
        <button bind:tap="navToLogin" class="btn" data-view="join" hoverClass="press-down-button" plain="true">
            <text class="p1 text__zh fs-13">立即加入</text>
        </button>
        <button bindtap="startGuestCheckout" class="btn btn--light" hoverClass="press-down-button" plain="true" style="margin-top: 10px;border-color: #ccc;">
            <text class="p1 text__zh fs-13">以访客身份结算</text>
        </button>
        <view class="join" wx:if="{{!isMemberExclusive}}">
            <profile-button bind:onGetUserProfile="navToLogin" both="{{true}}" class="profile-button" data-view="login" noCache="{{true}}">
                <text style="color: #757575">已是Nike会员？</text>
                <text class="link">登录</text>
            </profile-button>
        </view>
    </view>
    <view class="panel-bottom"></view>
    <join-login-panel bind:close="handleCloseJoinOrLoginPanel" id="join-login-panel" isOpen="{{showLoginJoinPanel}}" isUserInfoAuthorized="{{isUserInfoAuthorized}}"></join-login-panel>
</panel>
