Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.normalizeFulfillmentOfferingsRequestBody = r, exports.normalizeFulfillmentOfferingsV2RequestBody = function(e) {
  return {
    request: r(e)
  }
};
var e = require("../../../utils/common"),
  i = require("../../../utils/shipping");

function r(r) {
  var o = r.country,
    s = void 0 === o ? "CN" : o,
    t = r.currency,
    n = void 0 === t ? "CNY" : t,
    u = r.locale,
    d = void 0 === u ? "zh_CN" : u,
    p = r.items,
    l = void 0 === p ? [] : p,
    a = r.promotionCodes,
    f = void 0 === a ? [] : a,
    c = r.shippingAddress,
    m = r.offeringTypes,
    y = void 0 === m ? ["SHIP"] : m;
  return {
    country: s,
    currency: n,
    locale: d,
    items: l.map((function(r) {
      return {
        id: (0, e.uuid)(),
        skuId: r.skuId,
        quantity: r.quantity || 1,
        locations: [{
          type: "address/shipping",
          postalAddress: (0, i.shippingAddressNormalizer)(r.shippingAddress || c)
        }]
      }
    })),
    promotionCodes: f,
    offeringTypes: y
  }
}
exports.default = r;