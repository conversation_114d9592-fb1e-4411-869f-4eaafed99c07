<view>
    <image-loader adjustAspectRatio="{{adjustAspectRatio}}" aspectRatio="{{aspectRatio}}" binderror="handleImageError" bindload="handleImageLoaded" delayAppearance="{{delayAppearance}}" key="{{key}}" lazy="{{lazy}}" lazyLoadMargin="{{lazyLoadMargin}}" mode="{{mode}}" src="{{url}}" width="{{width}}" widthStr="{{widthStr}}" withSwoosh="{{withSwoosh}}" wx:if="{{!isSecondaryLoaded||!isLoaded}}"></image-loader>
    <image binderror="handleSecondaryError" bindload="handleSecondaryLoaded" src="{{secondaryUrl}}" style="{{isSecondaryLoaded&isLoaded?style:hide}}" wx:if="{{secondaryUrl}}"></image>
</view>
