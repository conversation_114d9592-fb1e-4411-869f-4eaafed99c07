var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/objectSpread2"),
  n = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.decryptWeChatEnterprise = function(e) {
  return c.wechatApiInst.post("/wechat_auth/wcw_user_info/v1", e)
}, exports.deleteProfileField = function(e, n, r) {
  var a = "".concat(u.NIKE_API_BASE_URL, "/identity/user/v1/").concat(e, "/field_delete"),
    o = getApp().globalData.appConfig.accounts.uxId;
  return (0, c.nikeIdnAccountInst)().post(a, t({}, r), {
    header: {
      Authorization: "Bearer ".concat(n),
      "x-nike-ux-id": o
    }
  }).then((function(e) {
    return e
  }))
}, exports.fetchUserInfo = void 0, exports.getNikePlusPass = function() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    t = e.timeoutRetry;
  return (0, c.nikeApiInst)({
    timeoutRetry: t
  }).request({
    method: "POST",
    data: {
      imageType: "SVG"
    },
    header: {
      "Content-Type": "application/json",
      Accept: "image/svg+xml"
    },
    url: "/membership/member_pass_images/v1"
  }).then((function(e) {
    return e.data
  }))
}, exports.getUserSharedProfile = function(e) {
  return (0, c.nikeApiInst)().get("/user/sharedprofile", {
    header: {
      Authorization: "Bearer ".concat(e)
    }
  }).then((function(e) {
    return e.data
  }))
}, exports.getWechatMiniOpenId = function(e) {
  return s.apply(this, arguments)
}, exports.getWechatminiProfile = function(e, t) {
  var n = "".concat(u.NIKE_API_BASE_URL, "/identity/user/v1/").concat(e, "/read"),
    r = getApp().globalData.appConfig.accounts.uxId;
  return (0, c.nikeIdnAccountInst)().post(n, {
    fields: ["archetype", "contact", "dob", "gender", "location", "name", "preferences", "measurements", "registration", "avatar"]
  }, {
    header: {
      Authorization: "Bearer ".concat(t),
      "x-nike-ux-id": r
    }
  }).then((function(e) {
    var t = e.data.dob;
    if (t) {
      var n = t.month,
        r = t.day,
        a = t.year;
      e.data.dob.month < 10 && (n = "0".concat(n)), e.data.dob.day < 10 && (r = "0".concat(r));
      var o = new Date("".concat(a, "-").concat(n, "-").concat(r)).getTime();
      e.data.dob.date = o
    }
    return e.data
  }))
}, exports.loginToWeChatEnterprise = function(e) {
  return c.wechatApiInst.post("/wechat_auth/wcw_login/v1", {
    code: e,
    corpId: u.WECHAT_ENTERPRISE_CORP_ID,
    agentId: (0, r.isPlusBeta)() ? u.WECHAT_ENTERPRISE_PLUS_BETA_AGENT_ID : u.WECHAT_ENTERPRISE_NIKE_PLUS_AGENT_ID
  })
}, exports.requestToFetchToken = function(e) {
  var t = e.code,
    n = e.codeVerifier,
    r = getApp().globalData.appConfig.accounts,
    a = r.url,
    o = r.redirectUri,
    i = r.clientId;
  return (0, c.nikeApiInst)().post("".concat(a, "/token/v1"), {
    redirect_uri: o,
    grant_type: "authorization_code",
    code: t,
    client_id: i,
    code_verifier: n
  }, {
    withoutToken: !0,
    header: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  }).then((function(e) {
    return e
  }))
}, exports.requestToRefreshToken = function(e) {
  var t = getApp().globalData.appConfig.accounts,
    n = t.url,
    r = t.clientId;
  return (0, c.nikeApiInst)({
    timeoutRetry: !0
  }).request({
    url: "".concat(n, "/token/v1"),
    method: "POST",
    withoutToken: !0,
    header: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data: {
      refresh_token: e,
      client_id: r,
      grant_type: "refresh_token"
    }
  }).then((function(e) {
    return e.data
  }))
}, exports.requestToRevokeToken = function(e) {
  var t = getApp().globalData.appConfig.accounts,
    n = t.url,
    r = t.clientId;
  return (0, c.nikeApiInst)({
    timeoutRetry: !0
  }).request({
    url: "".concat(n, "/revoke/token/v1"),
    method: "POST",
    withoutToken: !0,
    header: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    data: {
      client_id: r,
      token: e
    }
  }).then((function(e) {
    return e.data
  }))
}, exports.sendCode = function(e) {
  var t = "/idn/phone/+86".concat(e);
  return (0, c.nikeApiInst)().put(t, {}, {
    header: {
      "Content-Type": "application/json",
      "Content-Locale": "zh_CN"
    }
  })
}, exports.unbind = function(e, t) {
  var n = "/social/v1/links/users/".concat(t, "/networks/wechatmini/link");
  return (0, c.nikeApiInst)().delete(n, {
    header: {
      Authorization: "Bearer ".concat(e)
    }
  }).then((function(e) {
    return e
  }))
}, exports.updateAvatar = function(e, t, n) {
  var r = "https://www.nike.com/profile/services/users/".concat(t, "/avatar?access_token=").concat(e),
    a = {
      crop: {
        xcoord: 0,
        ycoord: 0,
        cropHeight: n.height,
        cropWidth: n.width
      },
      preview: {
        filename: n.filename
      }
    };
  return (0, c.nikeApiInst)().put(r, a, {
    header: {
      "X-NIKE-APP-ID": "com.nike.cnds.wechatmini",
      "X-NIKE-USER-ID": t
    }
  }).then((function(e) {
    return e
  }))
}, exports.updateProfile = function(e, n, r) {
  var a = "".concat(u.NIKE_API_BASE_URL, "/identity/user/v1/").concat(e),
    o = getApp().globalData.appConfig.accounts.uxId;
  return (0, c.nikeIdnAccountInst)().put(a, t({}, r), {
    header: {
      Authorization: "Bearer ".concat(n),
      "x-nike-ux-id": o
    }
  }).then((function(e) {
    return e
  }))
}, exports.updateWechatAPIProfile = function(e) {
  return h.apply(this, arguments)
}, exports.uploadAvatar = function(e, t, n) {
  var r = "https://www.nike.com/profile/services/users/".concat(t, "/avatar?access_token=").concat(e);
  return i.default.uploadFile({
    url: r,
    filePath: n,
    name: "filedata",
    header: {
      "X-NIKE-APP-ID": "com.nike.cnds.wechatmini",
      "X-NIKE-USER-ID": t
    }
  })
}, exports.validateCode = function(e) {
  var t = "/idn/phone/".concat(e);
  return (0, c.nikeApiInst)().post(t)
}, exports.weChatAPILogin = function() {
  return d.apply(this, arguments)
};
var r = require("../utils/common"),
  a = require("../utils/cache"),
  o = p(require("../lib/dot-prop")),
  i = p(require("../lib/mp")),
  c = require("./common"),
  u = require("./constants");

function p(e) {
  return e && e.__esModule ? e : {
    default: e
  }
}

function s() {
  return (s = n(e().mark((function t(n) {
    var r;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return r = getApp().globalData.appConfig.appId, e.abrupt("return", c.wechatApiInst.post("/wechat_auth/weChatMiniOpenIdExchange/v1", {
            appId: "wechat:mp:".concat(r),
            code: n
          }).then((function(e) {
            return {
              openid: e.data.openId,
              unionid: e.data.unionId
            }
          })));
        case 2:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}

function d() {
  return (d = n(e().mark((function t() {
    var n, u, p, s, d, h;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.prev = 0, e.next = 3, i.default.login();
        case 3:
          return n = e.sent, u = n.code, p = (0, r.getWechatApiInstAppId)(), s = (0, a.loadCache)("auth"), d = o.default.get(s, "data.accessToken"), h = {}, d && (h.Authorization = "Bearer ".concat(d)), e.next = 12, c.wechatApiInst.post("wechat_auth/token/v1", {
            appId: p,
            code: u
          }, {
            withoutToken: !0,
            header: h
          });
        case 12:
          return e.abrupt("return", e.sent);
        case 15:
          return e.prev = 15, e.t0 = e.catch(0), e.abrupt("return", Promise.reject(e.t0));
        case 18:
        case "end":
          return e.stop()
      }
    }), t, null, [
      [0, 15]
    ])
  })))).apply(this, arguments)
}

function h() {
  return (h = n(e().mark((function t(n) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.abrupt("return", c.wechatApiInst.post("wechat_profile/save_my_info/v1", n));
        case 1:
        case "end":
          return e.stop()
      }
    }), t)
  })))).apply(this, arguments)
}
exports.fetchUserInfo = function(e) {
  var t = getApp().globalData.appConfig.accounts.url;
  return (0, c.nikeApiInst)().get("".concat(t, "/user_info/v1"), {
    withoutToken: !0,
    header: {
      Authorization: "Bearer ".concat(e)
    }
  }).then((function(e) {
    return e.data
  }))
};