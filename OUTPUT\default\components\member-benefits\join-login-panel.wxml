<import src="./components/event-widget/index.wxml"></import>
<view class="jl-panel" wx:if="{{isOpen}}">
    <panel bind:close="handleCloseJoinOrLoginPanel" isOpen="{{isJoinOrLoginPanelOpen}}">
        <view class="panel-header ff-tg g-flex g-items-center">
            <text>注册 NIKE 会员</text>
            <view bindtap="handleCloseJoinOrLoginPanel" class="close-btn g-flex justify-content-center">
                <template is="panel-switch" data="{{isPanelOpen:isJoinOrLoginPanelOpen}}"></template>
            </view>
        </view>
        <view class="p3 c-9a plr-40 ff-tg" style="margin-bottom:26px;">
            <text>授权微信允许耐克获取你的手机号快速注册 NIKE 会员</text>
        </view>
        <view class="cta">
            <button bind:getphonenumber="getPhoneNumber" bindtap="handleGetPhoneNumberTap" class="btn" hoverClass="press-down-button" openType="getPhoneNumber" style="background-color:#1CAC1A;">
                <image src="https://mp-static-assets.gc.nike.com/image/svg/wechat.svg" style="width:24px;height:24px;margin-right:10px;filter: brightness(100)"></image>
                <text>一键注册</text>
            </button>
        </view>
        <view class="p3 ta-c" style="padding:14px 0;">
            <text class="c-9a ff-tg">已是 NIKE 会员？</text>
            <text bindtap="handleLoginTap" class="link">立即登录</text>
        </view>
        <view class="iPhoneX-placeholder" wx:if="{{isIPhoneX}}"></view>
    </panel>
    <panel bind:close="handleCloseJoinByOtherPhonePanel" isOpen="{{isJoinByOtherPhonePanelOpen}}">
        <view class="panel-header ff-tg g-flex g-items-center">
            <text>注册 NIKE 会员</text>
            <view bindtap="handleCloseJoinByOtherPhonePanel" class="close-btn">
                <template is="panel-switch" data="{{isPanelOpen:isJoinByOtherPhonePanelOpen}}"></template>
            </view>
        </view>
        <view class="p3 c-9a plr-40 ff-tg" style="margin-bottom:26px;">
            <text>如果不愿意授权微信，你仍可以使用其他手机号完成注册</text>
        </view>
        <view class="cta">
            <button bindtap="handleJoinByOtherPhoneTap" class="btn btn--ghost--dark">
                <text>使用其他手机号注册</text>
            </button>
        </view>
        <view class="p3 ta-c" style="padding:14px 0;">
            <text bindtap="handleQuitJoinForNowTap" class="link">暂不注册</text>
        </view>
        <view class="iPhoneX-placeholder" wx:if="{{isIPhoneX}}"></view>
    </panel>
</view>
