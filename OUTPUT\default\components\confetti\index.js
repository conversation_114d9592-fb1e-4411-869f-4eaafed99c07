var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/objectSpread2"),
  r = require("../../@babel/runtime/helpers/asyncToGenerator"),
  n = require("./confetti.js");
Component({
  options: {
    addGlobalClass: !0
  },
  data: {},
  methods: {
    initConfetti: function() {
      var e = this;
      return new Promise((function(t, r) {
        e.myConfetti && t(e.canvas), e.createSelectorQuery().select("#confetti-canvas").fields({
          node: !0,
          size: !0
        }).exec((function(i) {
          i[0] || r();
          var a = i[0].node,
            o = (0, n.confettiCannon)(a, {
              resize: !1
            });
          a.width = i[0].width, a.height = i[0].height, e.myConfetti = o, e.canvas = a, t(a)
        }))
      }))
    },
    boom: function() {
      var n = this;
      return r(e().mark((function r() {
        var i;
        return e().wrap((function(e) {
          for (;;) switch (e.prev = e.next) {
            case 0:
              if (e.t0 = n.myConfetti, e.t0) {
                e.next = 4;
                break
              }
              return e.next = 4, n.initConfetti();
            case 4:
              i = {
                particleCount: 32,
                startVelocity: 54,
                shapes: ["circle", "circle", "circle", "circle", "square", "square", "square", "square", "rectangle"],
                colors: ["#D76535", "#B7491F", "#F1997D", "#D05E2A"],
                scalar: 1.4,
                gravity: 1.5,
                ticks: 350
              }, n.myConfetti(t(t({}, i), {}, {
                angle: 75,
                drift: 1,
                origin: {
                  x: 0,
                  y: .7
                }
              })), n.myConfetti(t(t({}, i), {}, {
                angle: 105,
                drift: -1,
                origin: {
                  x: 1,
                  y: .7
                }
              }));
            case 6:
            case "end":
              return e.stop()
          }
        }), r)
      })))()
    }
  }
});