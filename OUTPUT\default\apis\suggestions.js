var e = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.findAutoSuggestions = n;
var t = require("./common"),
  o = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../vendor/qs"));

function n() {
  var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
    r = n.filter,
    i = n.timeoutInMilliseconds,
    s = n.timeoutRetry,
    u = void 0 === s || s,
    a = o.default.stringify(e({
      country: "cn",
      language: "zh-hans",
      text: encodeURIComponent(r.text)
    }, r), {
      indices: !1
    });
  return (0, t.nikeApiInst)({
    timeoutRetry: u
  }).get("/search/suggestions/v1?".concat(a, "&searchOptions=suggestionFlow:new"), {
    timeout: i
  })
}
exports.default = n;