<import src="./components/event-widget/index.wxml"></import>
<import src="./components/first-purchase/action-card.wxml"></import>
<form reportSubmit bind:submit="onSaveFormId">
    <view class="memberWall {{colorTheme}}" style="height: {{height}}">
        <swiper autoplay="{{autoPlay}}" bindchange="_updateTheme" class="swiper" duration="500" interval="{{interval}}">
            <swiper-item wx:for="{{contentMode==='memberIntro'?memberIntroImages:memberWallImages}}" wx:key="title">
                <image class="slide" mode="aspectFill" src="{{item.imageURL}}"></image>
                <image class="lockup {{contentMode}}" mode="scaleToFill" src="{{assetsHost}}/image/Membership_Lockup_{{item.colorTheme==='dark'?'Black':'Light'}}.png"></image>
            </swiper-item>
        </swiper>
        <view class="nav" style="bottom: {{navBottom}}">
            <view class="content" wx:if=" {{contentMode==='memberIntro'?!!memberIntroImages[imageIndex]:!!memberWallImages[imageIndex]}}">
                <view>
                    <text class="mb-title">{{contentMode==='memberIntro'?memberIntroImages[imageIndex].title:memberWallImages[imageIndex].title}}</text>
                </view>
                <view>
                    <text>{{contentMode==='memberIntro'?memberIntroImages[imageIndex].subtitle:memberWallImages[imageIndex].subtitle}}</text>
                </view>
            </view>
            <view class="join-panel">
                <view bind:tap="onFirstPurchaseRuleInfo" class="subtitle g-flex g-flex-center" hoverClass="{{isFirstPurchaseActive?'press-down-button':''}}">
                    <text class="p2 c-9a">{{isFirstPurchaseActive?threadContent.subtitle:'和我们一起'}}</text>
                    <image class="info" src="https://mp-static-assets.gc.nike.com/image/shop/info-grey.svg" wx:if="{{isFirstPurchaseActive}}"></image>
                </view>
                <text class="title h6 c-00">{{isFirstPurchaseActive?threadContent.main.title:'创造运动的未来'}}</text>
                <profile-button bind:onGetUserProfile="_handleConnectClick" both="{{true}}" data-view="join" noCache="{{true}}" uClass="join-btn btn btn--medium">注册登录</profile-button>
            </view>
            <template is="carouselIndicator" data="{{width:36,class:'indicator',data:contentMode==='memberIntro'?memberIntroImages:memberWallImages,currentIndex:imageIndex,colorTheme:colorTheme}}" wx:if="{{contentMode==='memberIntro'?memberIntroImages.length>1:memberWallImages.length>1}}"></template>
        </view>
    </view>
</form>
<join-login-panel bind:close="handleCloseJoinOrLoginPanel" id="join-login-panel" isOpen="{{showLoginJoinPanel}}" isUserInfoAuthorized="{{isUserInfoAuthorized}}"></join-login-panel>
