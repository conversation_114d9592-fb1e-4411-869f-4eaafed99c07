var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.default = void 0, exports.getCheckoutPreviewsJob = f, exports.getCheckoutSubmitJob = C, exports.getCheckoutsJob = b, exports.getFulfillmentOfferingsJob = m, exports.getHeader = void 0, exports.getPaymentPreviewJob = w, exports.getShippingOptions = function(e, r) {
  return d.apply(this, arguments)
}, exports.postPaymentPreview = x, exports.putCheckoutPreviews = p, exports.putCheckouts = g, exports.putFulfillmentOfferings = h, exports.requestCheckoutPreviews = function(e, r, t) {
  return I.apply(this, arguments)
}, exports.requestCheckouts = function(e, r, t) {
  return P.apply(this, arguments)
}, exports.requestFulfillmentOfferings = function(e, r, t) {
  return y.apply(this, arguments)
}, exports.requestPaymentPreviews = function(e, r, t) {
  return q.apply(this, arguments)
};
var t = require("../lib/dot-prop"),
  n = require("./common"),
  u = require("./normalizers/request/checkouts"),
  i = require("./normalizers/request/shipping"),
  o = require("./normalizers/response/checkouts"),
  s = require("../utils/payment-error"),
  a = require("../utils/shipping");
var c = function(e) {
  var r = {};
  return "string" == typeof e && "" !== e && (r["X-B3-TraceId"] = e), r
};

function p(e, r, t) {
  return l.apply(this, arguments)
}

function l() {
  return (l = r(e().mark((function r(t, u, i) {
    var o;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = "/buy/checkout_previews/v3/".concat(t), e.abrupt("return", (0, n.nikeApiInst)({
            timeoutRetry: !0,
            usingWxCloud: !0
          }).put(o, u, i));
        case 2:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function f(e, r) {
  return (0, n.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/checkout_previews_jobs/v3/".concat(e), r)
}

function d() {
  return (d = r(e().mark((function r(t, u) {
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.abrupt("return", (0, n.nikeApiInst)({
            timeoutRetry: !0,
            usingWxCloud: !0
          }).post("/buy/shipping_options/v2", t, u));
        case 1:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function h(e, r, t) {
  return v.apply(this, arguments)
}

function v() {
  return (v = r(e().mark((function r(t, u, i) {
    var o;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = "/buy/fulfillment_offerings_jobs/v2/".concat(t), e.abrupt("return", (0, n.nikeApiInst)({
            usingWxCloud: !0
          }).put(o, u, i));
        case 2:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function m(e, r) {
  return (0, n.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/fulfillment_offerings_jobs/v2/".concat(e), r)
}

function y() {
  return (y = r(e().mark((function u(a, c, p) {
    var l, f, d, v, y;
    return e().wrap((function(u) {
      for (;;) switch (u.prev = u.next) {
        case 0:
          if (Object.keys(c.shippingAddress).length) {
            u.next = 2;
            break
          }
          return u.abrupt("return", !1);
        case 2:
          return l = {
            request: [i.normalizeFulfillmentOfferingsV2RequestBody],
            response: [o.normalizeFulfillmentOfferingsResponse]
          }, f = function() {
            var t = r(e().mark((function r() {
              var t;
              return e().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    return t = l.request.reduce((function(e, r) {
                      return r(e)
                    }), c), e.next = 3, h(a, t, {
                      header: p
                    });
                  case 3:
                    return e.abrupt("return", e.sent);
                  case 4:
                  case "end":
                    return e.stop()
                }
              }), r)
            })));
            return function() {
              return t.apply(this, arguments)
            }
          }(), d = function() {
            return m(a, {
              header: p
            })
          }, v = function(e) {
            var r;
            if (401 == +(null === (r = e.header) || void 0 === r ? void 0 : r.status)) return s.FulfillmentOfferingsUnauthorized;
            var n = function(e) {
              return (0, t.get)(e, "data") || (0, t.get)(e, "result.data")
            }(e);
            return n.error ? "TIME_UP" === n.error.code ? s.FulfillmentOfferingsTimeout : n.error : !!n.warnings && n
          }, u.prev = 3, u.next = 6, (0, n.asyncApiCallExecutor)({
            initialRequest: f,
            poll: d,
            failed: v
          });
        case 6:
          return y = u.sent, u.abrupt("return", y = l.response.reduce((function(e, r) {
            return r(e)
          }), y));
        case 10:
          return u.prev = 10, u.t0 = u.catch(3), u.abrupt("return", Promise.reject(u.t0));
        case 13:
        case "end":
          return u.stop()
      }
    }), u, null, [
      [3, 10]
    ])
  })))).apply(this, arguments)
}

function g(e, r, t) {
  return k.apply(this, arguments)
}

function k() {
  return (k = r(e().mark((function r(t, u, i) {
    var o;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return o = "/buy/checkouts/v3/".concat(t), e.abrupt("return", (0, n.nikeApiInst)({
            usingWxCloud: !0
          }).put(o, u, i));
        case 2:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}

function b(e, r) {
  return (0, n.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/buy/checkouts_jobs/v3/".concat(e), r)
}

function x(e, r) {
  return (0, n.nikeApiInst)({
    usingWxCloud: !0
  }).post("/payment/preview/v3", e, r)
}

function w(e, r) {
  return (0, n.nikeApiInst)({
    timeoutRetry: !0,
    usingWxCloud: !0
  }).get("/payment/preview/v3/jobs/".concat(e), r)
}

function C(e, r) {
  var t = function(e) {
    return "job" === e.resourceType ? e.links.self.ref : "/buy/checkouts/v2/jobs/".concat(e.id)
  }(e);
  return (0, n.nikeApiInst)({
    timeoutRetry: !0
  }).get(t, r)
}

function I() {
  return (I = r(e().mark((function i(a, l, d) {
    var h, v, m, y, g, k;
    return e().wrap((function(i) {
      for (;;) switch (i.prev = i.next) {
        case 0:
          return h = {
            request: [u.normalizeCheckoutPreviewsRequestBody],
            response: [o.normalizeCheckoutPreviewsResponse]
          }, v = c(d.b3TraceId), m = function() {
            var t = r(e().mark((function r() {
              var t;
              return e().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    return t = h.request.reduce((function(e, r) {
                      return r(e)
                    }), l), e.next = 3, p(a, t, {
                      header: v
                    });
                  case 3:
                    return e.abrupt("return", e.sent);
                  case 4:
                  case "end":
                    return e.stop()
                }
              }), r)
            })));
            return function() {
              return t.apply(this, arguments)
            }
          }(), y = function() {
            return f(a, {
              header: v
            })
          }, g = function(e) {
            var r = function(e) {
              return (0, t.get)(e, "data") || (0, t.get)(e, "result.data")
            }(e);
            return !!r.error && ("TIME_UP" === r.error.code ? s.CheckoutPreviewTimeout : r.error)
          }, i.prev = 1, i.next = 4, (0, n.asyncApiCallExecutor)({
            initialRequest: m,
            poll: y,
            failed: g
          });
        case 4:
          return k = i.sent, i.abrupt("return", k = h.response.reduce((function(e, r) {
            return r(e)
          }), k));
        case 8:
          return i.prev = 8, i.t0 = i.catch(1), i.abrupt("return", Promise.reject(i.t0));
        case 11:
        case "end":
          return i.stop()
      }
    }), i, null, [
      [1, 8]
    ])
  })))).apply(this, arguments)
}

function q() {
  return (q = r(e().mark((function r(u, i, o) {
    var p, l, f, d, h, v, m, y, g, k, b, C, I, q, P, R, A, z, O, T;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return p = i.items, l = i.total, f = i.shippingOptionId, d = i.shippingAddress, h = i.contactInfo, v = i.recipient, m = i.channel, y = void 0 === m ? "WECHAT" : m, g = i.country, k = void 0 === g ? "CN" : g, b = i.currency, C = void 0 === b ? "CNY" : b, I = i.locale, q = void 0 === I ? "zh_CN" : I, P = i.paymentInfo, R = void 0 === P ? [] : P, A = c(o.b3TraceId), z = function() {
            return x({
              items: p.map((function(e) {
                var r = e.fulfillmentOfferings.find((function(e) {
                  return e.priceOfferId === f
                }));
                return {
                  productId: e.productId,
                  fulfillmentDetails: {
                    type: "SHIP",
                    getBy: r.getBy,
                    location: {
                      postalAddress: (0, a.shippingAddressNormalizer)(d),
                      type: "address/shipping"
                    }
                  },
                  recipient: v,
                  contactInfo: h
                }
              })),
              total: l,
              locale: q,
              country: k,
              currency: C,
              channel: y,
              checkoutId: u,
              paymentInfo: R
            }, {
              header: A
            })
          }, O = function(e) {
            return w(e.data.id, {
              header: A
            })
          }, T = function(e) {
            var r = function(e) {
              return (0, t.get)(e, "data") || (0, t.get)(e, "result.data")
            }(e);
            return !!r.error && ("TIME_UP" === r.error.code ? s.CheckoutPreviewTimeout : r.error)
          }, e.prev = 2, e.next = 5, (0, n.asyncApiCallExecutor)({
            initialRequest: z,
            poll: O,
            failed: T
          });
        case 5:
          return e.abrupt("return", e.sent);
        case 8:
          return e.prev = 8, e.t0 = e.catch(2), e.abrupt("return", Promise.reject(e.t0));
        case 11:
        case "end":
          return e.stop()
      }
    }), r, null, [
      [2, 8]
    ])
  })))).apply(this, arguments)
}

function P() {
  return (P = r(e().mark((function u(i, o, p) {
    var l, f, d, h, v, m, y, k, x, w, C, I, q, P, R, A, z, O, T, _, j, B, F;
    return e().wrap((function(u) {
      for (;;) switch (u.prev = u.next) {
        case 0:
          return l = o.items, f = o.phoneNumber, d = o.recipient, h = o.contactInfo, v = o.invoiceInfo, m = o.paymentToken, y = o.shippingOptionId, k = o.shippingAddress, x = o.channel, w = void 0 === x ? "WECHAT" : x, C = o.country, I = void 0 === C ? "CN" : C, q = o.locale, P = void 0 === q ? "zh_CN" : q, R = o.currency, A = void 0 === R ? "CNY" : R, z = o.promotionCodes, O = void 0 === z ? [] : z, T = o.totals, _ = c(p.b3TraceId), j = function() {
            var t = r(e().mark((function r() {
              var t;
              return e().wrap((function(e) {
                for (;;) switch (e.prev = e.next) {
                  case 0:
                    return t = {
                      request: {
                        phoneNumber: f,
                        items: l.map((function(e) {
                          var r = e.fulfillmentOfferings.find((function(e) {
                            return e.priceOfferId === y
                          }));
                          return {
                            id: e.id,
                            skuId: e.skuId,
                            offer: e.offer,
                            quantity: e.quantity,
                            recipient: d,
                            contactInfo: h,
                            fulfillmentDetails: {
                              type: "SHIP",
                              getBy: r.getBy,
                              location: {
                                postalAddress: (0, a.shippingAddressNormalizer)(k),
                                type: "address/shipping"
                              },
                              validationToken: r.validationToken
                            }
                          }
                        })),
                        invoiceInfo: v,
                        locale: P,
                        country: I,
                        channel: w,
                        currency: A,
                        paymentToken: m,
                        promotionCodes: O,
                        totals: T
                      }
                    }, e.next = 3, g(i, t, {
                      header: _
                    });
                  case 3:
                    return e.abrupt("return", e.sent);
                  case 4:
                  case "end":
                    return e.stop()
                }
              }), r)
            })));
            return function() {
              return t.apply(this, arguments)
            }
          }(), B = function(e) {
            return b(e.data.id, {
              header: _
            })
          }, F = function(e) {
            var r = function(e) {
              return (0, t.get)(e, "data") || (0, t.get)(e, "result.data")
            }(e);
            return !!r.error && ("TIME_UP" === r.error.code ? s.CheckoutPreviewTimeout : r.error)
          }, u.prev = 2, u.next = 5, (0, n.asyncApiCallExecutor)({
            initialRequest: j,
            poll: B,
            failed: F
          });
        case 5:
          return u.abrupt("return", u.sent);
        case 8:
          return u.prev = 8, u.t0 = u.catch(2), u.abrupt("return", Promise.reject(u.t0));
        case 11:
        case "end":
          return u.stop()
      }
    }), u, null, [
      [2, 8]
    ])
  })))).apply(this, arguments)
}
exports.getHeader = c;
exports.default = {
  api: {
    putCheckoutPreviews: p,
    getCheckoutPreviewsJob: f,
    getCheckoutSubmitJob: C
  },
  normalizer: {
    request: {
      normalizeCheckoutPreviewsRequestBody: u.normalizeCheckoutPreviewsRequestBody,
      normalizeFulfillmentOfferingsV2RequestBody: i.normalizeFulfillmentOfferingsV2RequestBody
    },
    response: {
      normalizeCheckoutPreviewsResponse: o.normalizeCheckoutPreviewsResponse,
      normalizeFulfillmentOfferingsResponse: o.normalizeFulfillmentOfferingsResponse
    }
  },
  options: {
    getHeader: c
  }
};