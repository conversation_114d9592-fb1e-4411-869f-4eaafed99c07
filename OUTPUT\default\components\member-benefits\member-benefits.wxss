.memberWall {
    font-family: Helvetica Neue,sans-serif;
    height: 100%;
    max-height: 100vh;
    position: relative
}

.memberWall .swiper {
    background: #111;
    height: 100%;
    width: 100%
}

.memberWall .swiper .slide {
    height: 100%;
    position: relative;
    -webkit-transform-origin: center center;
    transform-origin: center center;
    width: 100%
}

.memberWall .swiper .lockup {
    position: fixed
}

.memberWall .swiper .lockup.memberIntro {
    height: 34px;
    left: 20px;
    top: 55px;
    width: 125px
}

.memberWall .swiper .lockup.memberWall {
    height: 40px;
    left: 20px;
    top: 108px;
    width: 147px
}

.memberWall .nav {
    color: #111;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    font-size: 13px;
    left: 40rpx;
    line-height: 1.4;
    position: absolute;
    right: 40rpx;
    text-align: center
}

.memberWall .nav .join-panel {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 15px 0 rgba(0,0,0,.1);
    height: 164rpx;
    position: relative
}

.memberWall .nav .join-panel .subtitle {
    left: 40rpx;
    letter-spacing: 3px;
    position: absolute;
    top: 40rpx
}

.memberWall .nav .join-panel .info {
    height: 13px;
    margin-left: 5px;
    width: 13px
}

.memberWall .nav .join-panel .title {
    bottom: 40rpx;
    left: 40rpx;
    position: absolute
}

.memberWall .nav .join-panel .join-btn.btn {
    background-color: #111;
    color: #fff;
    height: 42px;
    line-height: 42px;
    position: absolute;
    right: 40rpx;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.memberWall .content {
    font-weight: 400;
    margin-bottom: 18px;
    text-align: left
}

.memberWall .content .mb-title {
    font-weight: 700
}

.memberWall .indicator {
    -webkit-align-self: center;
    align-self: center
}

.memberWall .hasAccount {
    color: #9a9a9a
}

.memberWall .login {
    color: #111
}

.memberWall.light .content,.memberWall.light .nav {
    color: #fff
}

.memberWall.light .content .btn,.memberWall.light .content .indicatorItem.active,.memberWall.light .nav .btn,.memberWall.light .nav .indicatorItem.active {
    background-color: #fff;
    color: #111
}

.memberWall.light .content .link,.memberWall.light .nav .link {
    border-color: #fff;
    color: #fff
}

.memberWall .link {
    background: 0 0;
    border: 0;
    border-bottom: 1px solid;
    border-radius: 0;
    font-size: 13px;
    font-weight: 400;
    padding: 0;
    transition: color .2s,background-color .2s
}

.memberWall .link:active {
    opacity: .8
}
