var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../../utils/configureStore")),
  i = require("../../vendor/redux"),
  a = require("../../state/firstPurchase"),
  n = require("../../state/user"),
  s = require("../../utils/coupons"),
  u = require("../../utils/analytics/core"),
  o = require("../../lib/mp-redux/index");
var c = {
    options: {
      addGlobalClass: !0
    },
    data: {
      loggedIn: !1,
      isDisplayTrackSent: !1,
      isFadingOut: !1
    },
    pageLifetimes: {
      show: function() {
        var r = this;
        return t(e().mark((function t() {
          var i, a, n, s, o, c;
          return e().wrap((function(e) {
            for (;;) switch (e.prev = e.next) {
              case 0:
                return e.prev = 0, e.next = 3, r.ensureAuth();
              case 3:
                r.setData({
                  loggedIn: !0
                }), i = r.data, a = i.isShow, n = i.isNewMember, s = i.threadContent, o = i.isDisplayTrackSent, a && !o && ((0, u.sendTrackEvent)("Mp_1St_Purchase_Display", {
                  title: n ? s.title1 : s.title2,
                  threadId: s.threadId,
                  promoTitle: null === (c = s.main) || void 0 === c ? void 0 : c.title
                }), r.setData({
                  isDisplayTrackSent: !0
                })), e.next = 11;
                break;
              case 8:
                e.prev = 8, e.t0 = e.catch(0), r.setData({
                  loggedIn: !1
                });
              case 11:
              case "end":
                return e.stop()
            }
          }), t, null, [
            [0, 8]
          ])
        })))()
      }
    },
    methods: {
      onClaim: function() {
        var r = this;
        return t(e().mark((function i() {
          var a, n, o, c;
          return e().wrap((function(i) {
            for (;;) switch (i.prev = i.next) {
              case 0:
                return a = r.data.stockId, i.next = 3, r.triggerEvent("startLoadingTask", {
                  fn: function() {
                    var i = t(e().mark((function t() {
                      return e().wrap((function(e) {
                        for (;;) switch (e.prev = e.next) {
                          case 0:
                            return e.next = 2, (0, s.claimCoupon)({
                              stockId: a,
                              customeRuleLimitCb: function() {
                                r.setClaimed()
                              },
                              customeClaimSucceedCb: function() {
                                setTimeout((function() {
                                  r.setData({
                                    isFadingOut: !0
                                  })
                                }), 3500), setTimeout((function() {
                                  r.setClaimed()
                                }), 4500), (0, u.sendTrackEvent)("Mp_1St_Purchase_Claim_Success")
                              }
                            });
                          case 2:
                          case "end":
                            return e.stop()
                        }
                      }), t)
                    })));
                    return function() {
                      return i.apply(this, arguments)
                    }
                  }()
                }, {
                  bubbles: !0,
                  composed: !0
                });
              case 3:
                n = r.data, o = n.isNewMember, c = n.threadContent, (0, u.sendTrackEvent)("Mp_1St_Purchase_Claim", {
                  title: o ? c.title1 : c.title2,
                  threadId: c.threadId,
                  promoTitle: c.main.title,
                  buttonText: c.cta.text,
                  url: c.cta.url
                });
              case 5:
              case "end":
                return i.stop()
            }
          }), i)
        })))()
      },
      onSkip: function() {
        this.skipFirstPurchase();
        var e = this.data,
          t = e.isNewMember,
          r = e.threadContent;
        (0, u.sendTrackEvent)("Mp_1St_Purchase_Skip", {
          title: t ? r.title1 : r.title2,
          threadId: r.threadId,
          promoTitle: r.main.title
        })
      }
    }
  },
  l = {
    ensureAuth: n.ensureAuth,
    skipFirstPurchase: a.skipFirstPurchase,
    setClaimed: a.setClaimed
  };
(0, i.compose)(Component, (0, o.connectComponent)(r.default)((function(e, t) {
  var r, i = (0, a.isFirstPurchaseNotClaimed)(e) && (0, a.isFirstPurchaseActive)(e) && (0, a.isUserQualified)(e),
    n = (0, a.isNewMember)(e),
    s = (0, a.getFirstPurchaseThread)(e) || {},
    o = {
      isShow: i,
      isNewMember: n,
      threadContent: s,
      stockId: (0, a.getFirstPurchaseStockId)(e)
    },
    c = t.loggedIn,
    l = t.isDisplayTrackSent;
  return i && c && !l && ((0, u.sendTrackEvent)("Mp_1St_Purchase_Display", {
    title: n ? s.title1 : s.title2,
    threadId: s.threadId,
    promoTitle: null === (r = s.main) || void 0 === r ? void 0 : r.title
  }), o.isDisplayTrackSent = !0), o
}), l))(c);