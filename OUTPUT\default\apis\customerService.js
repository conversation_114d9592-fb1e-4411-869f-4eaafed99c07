var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../@babel/runtime/helpers/asyncToGenerator"),
  t = require("../@babel/runtime/helpers/objectSpread2");
Object.defineProperty(exports, "__esModule", {
  value: !0
}), exports.createUserToCS = function(e) {
  return c.apply(this, arguments)
}, exports.getUserFromCS = function(e) {
  return p.get("/v1/members/".concat(e))
}, exports.updateUserToCS = function(e, r) {
  return p.put("/v1/members/".concat(e), r)
};
var n = require("../lib/httpClient/index"),
  a = require("./constants"),
  o = function(e) {
    return e && e.__esModule ? e : {
      default: e
    }
  }(require("../lib/mp"));
var u = getApp().globalData.appConfig.customerService,
  i = u.api,
  s = u.token,
  p = new n.HttpClient({
    baseURL: i,
    header: t(t({}, a.COMMON_HEADER), {}, {
      Authorization: "token ".concat(s)
    })
  });

function c() {
  return (c = r(e().mark((function r(n) {
    var a, u, i;
    return e().wrap((function(e) {
      for (;;) switch (e.prev = e.next) {
        case 0:
          return e.next = 2, o.default.login();
        case 2:
          return a = e.sent, u = a.code, i = t(t({}, n), {}, {
            js_code: u
          }), e.abrupt("return", p.post("/v1/modules/nikeminiapp/member/create", i));
        case 6:
        case "end":
          return e.stop()
      }
    }), r)
  })))).apply(this, arguments)
}(0, n.addTimeTrack)(p), (0, n.addErrorReport)(p), (0, n.addRetry)(p);