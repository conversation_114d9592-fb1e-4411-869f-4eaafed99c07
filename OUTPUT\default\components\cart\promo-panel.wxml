<import src="./components/first-purchase/action-card.wxml"></import>
<import src="./components/member-store/product-rank-card.wxml"></import>
<panel bgZIndex="{{1000}}" bind:autoAdjustHeight="handleAutoAdjustHeight" bind:close="handleClosePanel" bind:resizePanel="resizePanel" bind:setHeight="handleSetHeight" id="cart-panel" isOpen="{{isOpen}}" transitionDuration="{{PANEL_TRANSITION_DURATION}}" transitionProperty="{{PANEL_TRANSITION_PROPERTY}}" transitionTimingFunction="{{PANEL_TRANSITION_TIMING_FUNCTION}}">
    <view class="container-root">
        <view class="container-content c-39">
            <view style="padding-bottom: {{dimensions.footer.height}}px;">
                <view bind:tap="tryCloseCartPanel" class="container-header dimension-query-node {{isAddPromoCodeFormVisible?'':'thin-border-bottom'}}" data-id="header" id="header" style="display:inline-flex;width:100%;">
                    <view>使用优惠券</view>
                    <view style="padding-top:8px;position:absolute;right:40rpx;">
                        <template is="panel-switch" data="{{isPanelOpen:true}}"></template>
                    </view>
                </view>
                <view class="dimension-query-node" id="content" style="max-height: {{contentMaxHeight}}px; overflow-y: scroll;">
                    <promo-code-input bind:hideCartPromoInput="hideCartPromoInputAndResize" id="promo-code-panel" wx:if="{{isAddPromoCodeFormVisible}}"></promo-code-input>
                    <view class="promo-code" wx:else>
                        <promo-list bind:addMorePromo="handleTryToAddPromo" bind:showAllPromo="resizePanel" bind:tapPromo="handleTapPromoSelection" bind:tapPromoTips="handleTapPromoTips" mode="cart" promos="{{allPromos}}" wx:if="{{isOpen}}"></promo-list>
                        <view class="thin-border-top thin-border-bottom" style="height:10px;background-color:#f7f7f7;display:block;margin:0"></view>
                        <view class="price-wrapper">
                            <view class="price" id="price">
                                <view class="price-item">
                                    <view class="price-title">实际商品总额</view>
                                    <view class="price-value">{{subtotalFormatted}}</view>
                                </view>
                                <view class="price-item">
                                    <view class="price-title">实际支付运费</view>
                                    <view class="price-value-free">{{shippingFee}}</view>
                                </view>
                                <view class="price-item" wx:if="{{!promotion.code}}" wx:for="{{promotionDiscounts}}" wx:for-item="promotion" wx:key="promotion">
                                    <view class="price-title">{{promotion.displayName}}</view>
                                    <view class="price-value">-¥{{promotion.amount}}</view>
                                </view>
                                <view class="price-item" wx:for="{{appliedPromos}}" wx:for-item="promo" wx:key="promo">
                                    <view class="price-title">{{promo.title}}优惠</view>
                                    <view class="price-value">-¥{{promo.discount}}</view>
                                </view>
                                <view style="margin-top: 24px; margin-bottom: 24px" wx:if="{{appliedCoupons.length>0}}">
                                    <view class="price-item">
                                        <view class="price-title">总计</view>
                                        <view class="price-value">{{cartSelectedTotal}}</view>
                                    </view>
                                    <view class="price-item" wx:for="{{appliedCoupons}}" wx:for-item="coupon" wx:key="coupon">
                                        <view class="price-title">{{coupon.couponName}}</view>
                                        <view class="price-value">-{{coupon.displayAmount}}</view>
                                    </view>
                                </view>
                                <view class="price-item price-item__sum" style="color: black; font-weight:normal;">
                                    <view>{{appliedCoupons.length>0?'预计支付':'总计'}}</view>
                                    <view>¥{{paymentAmount}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="cta cta--panel {{isAddPromoCodeFormVisible?'':'thin-border-top'}} {{isIphoneX?'cta--x':''}} dimension-query-node" id="footer">
                    <view class="g-flex g-items-center">
                        <button bind:tap="handleTapCartPanelCta" class="btn {{shouldEnableCta?'':'disabled'}}" disabled="{{!shouldEnableCta}}" hoverClass="press-down-button" id="btn-apply-promo-code">
                            <view class="btn--txt" wx:if="{{!isAddPromoCodeFormVisible}}">
                                <text class="btn__amount">¥{{paymentAmount}}</text>结算</view>
                            <view class="btn--txt" wx:else>使用</view>
                        </button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</panel>
